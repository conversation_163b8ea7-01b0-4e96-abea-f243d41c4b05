﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///合約申請主表
    ///</summary>
    [SugarTable("form_application")]
    public partial class form_application
    {
           public form_application(){


           }
           /// <summary>
           /// Desc:申請單號，規則:C+西元年+5碼流水號(次年重新計算)
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:填單人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string fill_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:填單人部門
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string fill_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:經辦人
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string pic_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:經辦人部門
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string pic_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:經辦人siteid/location
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string pic_siteid {get;set;} = null!;

           /// <summary>
           /// Desc:現任聯絡人，只有轉單後才會存在現任聯絡人數據
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? incumbent_emplid {get;set;}

           /// <summary>
           /// Desc:eAssign申請單號15碼 ex.AS2024042200014
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? eassign_number {get;set;}

           /// <summary>
           /// Desc:希望完成日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? expected_date {get;set;}

           /// <summary>
           /// Desc:合約到期/延展通知提醒
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? delay_reminder {get;set;}

           /// <summary>
           /// Desc:我方主體
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? entity_id {get;set;}

           /// <summary>
           /// Desc:他方為學校、研究或學術機構
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? is_school {get;set;}

           /// <summary>
           /// Desc:他方(含簡稱)	輸入公司全名，以json格式存儲
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_party {get;set;}

           /// <summary>
           /// Desc:他方合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_party_number {get;set;}

           /// <summary>
           /// Desc:合約名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_name {get;set;}

           /// <summary>
           /// Desc:合約背景摘要
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_bg_remark {get;set;}

           /// <summary>
           /// Desc:建議指定正本簽署主管；sys_parameters.para_code = N'suggestStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? is_original {get;set;}

           /// <summary>
           /// Desc:正本書面資料提供形式；sys_parameters.para_code = N'originalWriteType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? original_written_type {get;set;}

           /// <summary>
           /// Desc:用印種類；sys_parameters.para_code = N'sealStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? seal_type {get;set;}

           /// <summary>
           /// Desc:當 用印種類 選擇其他需求時，需要填寫
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_seal_type {get;set;}

           /// <summary>
           /// Desc:是否允許正本加工；sys_parameters.para_code = N'confidentialStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? confidential_identification {get;set;}

           /// <summary>
           /// Desc:當 是否允許正本加工 選擇不可註記時，需要填寫
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? confidential_remark {get;set;}

           /// <summary>
           /// Desc:當地合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? local_contract_number {get;set;}

           /// <summary>
           /// Desc:其他備註/說明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? application_remark {get;set;}

           /// <summary>
           /// Desc:是否有無金額；sys_parameters.para_code = N'amountStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? is_having_money {get;set;}

           /// <summary>
           /// Desc:是否載明罰則；1：是；0：否
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? is_penalty {get;set;}

           /// <summary>
           /// Desc:載明罰則原因 is_penaly== 1 時，為必填項
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? penalty_remark {get;set;}

           /// <summary>
           /// Desc:掛帳部門名稱，當存在金額時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? account_deptid {get;set;}

           /// <summary>
           /// Desc:承辦法務人員工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? legal_affairs_emplid {get;set;}

           /// <summary>
           /// Desc:申請類別；sys_parameters.para_code = N'formType'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string form_type {get;set;} = null!;

           /// <summary>
           /// Desc:會簽類型，可能會存在多值；sys_parameters.para_code = N'acknowledgeType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? acknowledge_type {get;set;}

           /// <summary>
           /// Desc:案件狀態	T：暫存單(無必要參數，申請單號為經辦人工號)	I：進行中(申請單還在簽核過程中)	A：已核准	O：舊案件(由數據導入，非user申請)	D：案件已被刪除	E：已結案
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string application_state {get;set;} = null!;

           /// <summary>
           /// Desc:新舊案件狀態；1：舊案件；0：新案件
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int is_new {get;set;}

           /// <summary>
           /// Desc:申請日期，格式：2011-07-06 12:34:56
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_time {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人員
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:承辦法務人員部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? legal_affairs_deptid {get;set;}

           /// <summary>
           /// Desc:機密等級；sys_parameters.para_code = N'confidentStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? confiden_level {get;set;}

           /// <summary>
           /// Desc:結案日期，合約管理作者者點擊end的日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? closed_date {get;set;}

           /// <summary>
           /// Desc:經辦人Location（具體辦公位置）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_desk_location {get;set;}

           /// <summary>
           /// Desc:現任聯絡人部門，只有轉單後才會存在現任聯絡人數據
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? incumbent_deptid {get;set;}

           /// <summary>
           /// Desc:加簽者工號；當前關卡操作者
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? signatory {get;set;}

           /// <summary>
           /// Desc:已核准日期，合約管理作業關卡之前的結案日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? approved_date {get;set;}

           /// <summary>
           /// Desc:提前終止日期，只有在修改表單的時候會存在
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? early_cease_date {get;set;}

           /// <summary>
           /// Desc:是否為舊系統進入數據，1：是；0：否
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_old_system {get;set;}

           /// <summary>
           /// Desc:1.0有無修改合約/文件；0：有；1：無(默認)；O：舊資料
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? is_update {get;set;}

           /// <summary>
           /// Desc:1.0應補會簽人員
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? retroactive_signer {get;set;}

           /// <summary>
           /// Desc:1.0最終簽署人核准簽核判定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_decide {get;set;}

    }
}
