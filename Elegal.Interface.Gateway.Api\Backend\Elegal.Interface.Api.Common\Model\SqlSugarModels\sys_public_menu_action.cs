﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///公版角色菜單按鈕權限對照表
    ///</summary>
    [SugarTable("sys_public_menu_action")]
    public partial class sys_public_menu_action
    {
           public sys_public_menu_action(){


           }
           /// <summary>
           /// Desc:序號；自增長，主鍵
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:公版角色code(對應sys_public_role.rowid)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int public_role_code {get;set;}

           /// <summary>
           /// Desc:頁面code(對應sys_menu.menu_code)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string menu_code {get;set;} = null!;

           /// <summary>
           /// Desc:按鈕權限code(對應sys_action.cction_code)，ex：1,2,3,4
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string action_code {get;set;} = null!;

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
