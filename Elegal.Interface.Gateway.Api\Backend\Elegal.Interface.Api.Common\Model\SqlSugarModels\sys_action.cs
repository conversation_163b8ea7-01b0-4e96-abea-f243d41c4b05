﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///按鈕類型表
    ///</summary>
    [SugarTable("sys_action")]
    public partial class sys_action
    {
           public sys_action(){


           }
           /// <summary>
           /// Desc:序號；自增長；主鍵
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:按鈕id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int action_code {get;set;}

           /// <summary>
           /// Desc:按鈕中/英文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string action_name {get;set;} = null!;

           /// <summary>
           /// Desc:語係：ZH-TW / EN-US
           /// Default:ZH-TW
           /// Nullable:False
           /// </summary>           
           public string lang_type {get;set;} = null!;

           /// <summary>
           /// Desc:是否使用；1：使用；0：禁用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int is_used {get;set;}

           /// <summary>
           /// Desc:用戶提供的查詢排序規則
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sort_order {get;set;}

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
