﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///其他申請 -> C)：作廢
    ///</summary>
    [SugarTable("other_application_c")]
    public partial class other_application_c
    {
           public other_application_c(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public long rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與 other_application.apply_number 對應
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:作廢單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? voided_apply_number {get;set;}

           /// <summary>
           /// Desc:原合約紙本狀態；sys_parameters.para_code = N'otherApplyType'N'oldContractPaperStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_paper_status {get;set;}

           /// <summary>
           /// Desc:原合約紙本狀態其他原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_paper_reason {get;set;}

           /// <summary>
           /// Desc:申請原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_reason {get;set;}

           /// <summary>
           /// Desc:其他說明/備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_remark {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
