﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///菜單欄下對應的頁面(非正常頁面區塊)
    ///</summary>
    [SugarTable("sys_menu_page")]
    public partial class sys_menu_page
    {
           public sys_menu_page(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:掛在哪一個菜單欄下面，sys_menu.menu_code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long menu_code {get;set;}

           /// <summary>
           /// Desc:父頁面code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long parent_page_code {get;set;}

           /// <summary>
           /// Desc:頁面code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long page_code {get;set;}

           /// <summary>
           /// Desc:頁面中/英文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string page_name {get;set;} = null!;

           /// <summary>
           /// Desc:頁面部件名，ex：HomeComponent
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string page_component {get;set;} = null!;

           /// <summary>
           /// Desc:語係：ZH-TW / EN-US
           /// Default:ZH-TW
           /// Nullable:False
           /// </summary>           
           public string lang_type {get;set;} = null!;

           /// <summary>
           /// Desc:頁面等級，1：一級頁面；2：二級頁面；3：三級頁面
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int page_level {get;set;}

           /// <summary>
           /// Desc:頁面類型	1 -> 頁面	2 -> 頁面下某一個tab頁簽	3 -> 頁面下某一個區塊
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int page_type {get;set;}

           /// <summary>
           /// Desc:資料檢視權限對應的code
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? view_code {get;set;}

           /// <summary>
           /// Desc:按鈕對應的code
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? action_code {get;set;}

           /// <summary>
           /// Desc:排序，為頁面顯示準備
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int sort_order {get;set;}

           /// <summary>
           /// Desc:是否使用；1：使用；0：禁用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool is_used {get;set;}

           /// <summary>
           /// Desc:是否可給用戶進行自行控管；1：是；0：否
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_share {get;set;}

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:system
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
