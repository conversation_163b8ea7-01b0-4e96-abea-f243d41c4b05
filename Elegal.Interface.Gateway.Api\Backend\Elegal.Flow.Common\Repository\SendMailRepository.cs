﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.HRApi;
using Elegal.Interface.Api.Common.Repository;
using System.Dynamic;
using System.Reflection;
using Elegal.Interface.Api.Common.FuncHelper;

namespace Elegal.Flow.Common.Repository
{
    public class SendMailRepository : BaseRepository
    {
        public List<dynamic> GetFGetPaperLendingByMail(string lend_number, string current_signer)
        {
            string baseUtcOffset = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone).BaseUtcOffset.ToString();
            baseUtcOffset = (baseUtcOffset.IndexOf("-") > -1) ? baseUtcOffset.Substring(0, 6) : ("+" + baseUtcOffset.Substring(0, 5));
            int current_step = 0;

            string sql = $@"SELECT * FROM F_GetPaperLendingByMail(@lend_number, @current_signer, @current_step, @baseUtcOffset)";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, new { lend_number, current_signer, current_step, baseUtcOffset });
            }
            catch
            {
                return null;
            }
            return res;
        }

        public List<dynamic> GetFGetApplicationByMail(string apply_number, string current_signer)
        {
            string baseUtcOffset = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone).BaseUtcOffset.ToString();
            baseUtcOffset = (baseUtcOffset.IndexOf("-") > -1) ? baseUtcOffset.Substring(0, 6) : ("+" + baseUtcOffset.Substring(0, 5));
            int current_step = 0;

            string sql = $@"SELECT * FROM F_GetApplicationByMail(@apply_number, @current_signer, @current_step, @baseUtcOffset)";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, new { apply_number, current_signer, current_step, baseUtcOffset });
            }
            catch
            {
                return null;
            }
            return res;
        }

        public List<dynamic> GetFGetTransferByMail(string apply_number)
        {
            string baseUtcOffset = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone).BaseUtcOffset.ToString();
            baseUtcOffset = (baseUtcOffset.IndexOf("-") > -1) ? baseUtcOffset.Substring(0, 6) : ("+" + baseUtcOffset.Substring(0, 5));
            string sql = $@"SELECT * FROM F_GetTransferDetailByMail(@apply_number, @baseUtcOffset)";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, new { apply_number, current_login = MvcContext.UserInfo.current_emp , baseUtcOffset = baseUtcOffset });
            }
            catch
            {
                return null;
            }
            return res;
        }

        public List<dynamic> F_GetLendPaperRemindByMail(string apply_number, string mailType)
        {
            string sql = $@"SELECT * FROM F_GetLendPaperRemindByMail(@apply_number,@mailType)";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, new { apply_number, mailType });
            }
            catch
            {
                return null;
            }
            return res;
        }

        public List<dynamic> F_GetApplicationRemindByMail(string apply_number, string mailType)
        {
            string sql = $@"SELECT * FROM F_GetApplicationRemindByMail(@apply_number,@mailType)";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, new { apply_number, mailType });
            }
            catch
            {
                return null;
            }
            return res;
        }

        /// <summary>
        /// 查詢當日下載過多提醒
        /// </summary>
        /// <returns></returns>
        public List<dynamic> F_GetDownloadOverRemindDetail()
        {
            string sql = $@"SELECT * FROM F_GetDownloadOverRemindDetail()";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, null);
            }
            catch
            {
                return null;
            }
            return res;
        }

        public List<dynamic> F_GetConfigEmpDimissionRemindByMail(string apply_number)
        {
            //配置人員離職提醒加上按照設定類型排序20250226
            string sql = $@"SELECT * FROM F_GetConfigEmpDimissionRemindByMail()
                order by sort asc,stuff(el_pic_emplid_zh, 1, patindex('%[A-z]%', substring(el_pic_emplid_zh, 1, 1)) - 1, '') ASC,   
    	                len(el_pic_emplid_zh) DESC,   
    	                el_pic_emplid_zh DESC ";
            List<ConfigEmpDimissionModel> res;
            List<dynamic> resDynamic = new List<dynamic>();
            try
            {
                res = this.NpgsqlSearchByList<ConfigEmpDimissionModel>(sql, null);
                List<ConfigEmpDimissionModel> configEmpDimissionModels = res.GroupBy(s => new
                {
                    s.el_config_name_en,
                    s.el_config_name_zh,
                    s.el_pic_ename_zh,
                    s.el_pic_ename_en,
                    s.el_pic_emplid_zh,
                    s.el_pic_emplid_en,
                    s.el_config_type_zh,
                    s.el_config_type_en,
                }).Select(a => new ConfigEmpDimissionModel
                {
                    el_pic_emplid_en = a.Key.el_pic_emplid_en,
                    el_pic_emplid_zh = a.Key.el_pic_emplid_zh,
                    el_config_name_en = a.Key.el_config_name_en,
                    el_config_name_zh = a.Key.el_config_name_zh,
                    el_pic_ename_zh = a.Key.el_pic_ename_zh,
                    el_pic_ename_en = a.Key.el_pic_ename_en,
                    el_config_type_en = a.Key.el_config_type_en,
                    el_config_type_zh = a.Key.el_config_type_zh,
                    el_my_entity_en = string.Join(",", a.Where(s=>!string.IsNullOrEmpty(s.el_my_entity_en)).Select(item => item.el_my_entity_en)),
                    el_my_entity_zh = string.Join(",", a.Where(s => !string.IsNullOrEmpty(s.el_my_entity_en)).Select(item => item.el_my_entity_en))
                }).ToList();
                configEmpDimissionModels.ForEach(model => {
                     resDynamic.Add(ConvertToDynamic(model));
                });
            }
            catch
            {
                return null;
            }
            return resDynamic;
        }

        public static dynamic ConvertToDynamic(object obj)
        {
            // 创建一个 ExpandoObject 实例
            dynamic expando = new ExpandoObject();
            var expandoDict = (IDictionary<string, object>)expando;

            // 使用反射获取对象的类型信息
            Type type = obj.GetType();
            PropertyInfo[] properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            // 遍历属性，将属性名和值添加到 ExpandoObject 中
            foreach (PropertyInfo property in properties)
            {
                try
                {
                    expandoDict[property.Name] = property.GetValue(obj);
                }
                catch (Exception ex)
                {
                    // 处理异常，例如记录日志或采取其他措施
                    Console.WriteLine($"Error getting property {property.Name}: {ex.Message}");
                }
            }

            return expando;
        }

        public List<dynamic> GetFGetLegalTransferByMail(string apply_number)
        {
            string baseUtcOffset = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone).BaseUtcOffset.ToString();
            baseUtcOffset = (baseUtcOffset.IndexOf("-") > -1) ? baseUtcOffset.Substring(0, 6) : ("+" + baseUtcOffset.Substring(0, 5));
            string sql = $@"SELECT * FROM F_GetLegalTransferDetailByMail(@apply_number, @baseUtcOffset)";
            List<dynamic> res;
            try
            {
                res = this.NpgsqlSearchByList<dynamic>(sql, new { apply_number, current_login = MvcContext.UserInfo.current_emp , baseUtcOffset = baseUtcOffset });
            }
            catch
            {
                return null;
            }
            return res;
        }

        public paper_lending_application GetPaperLendingApplication(string lend_number)
        {
            string sql = $@"SELECT * FROM paper_lending_application WHERE lend_number = @lend_number";
            return NpgsqlSearchBySingle<paper_lending_application>(sql, new { lend_number });
        }

        public paper_lending_application GetPaperLendingApplicationByLendID(int lend_id)
        {
            string sql = $@"SELECT * FROM paper_lending_application WHERE lend_id = @lend_id";
            return NpgsqlSearchBySingle<paper_lending_application>(sql, new { lend_id });
        }

        public paper_lending_demand GetPaperLendingDemand(int paper_lend_id)
        {
            string sql = $@"SELECT * FROM paper_lending_demand WHERE paper_lend_id = @paper_lend_id";
            return NpgsqlSearchBySingle<paper_lending_demand>(sql, new { paper_lend_id });
        }

        public List<paper_lending_detail> GetPaperLendingDetail(int paper_lend_id)
        {
            string sql = $@"SELECT pld.detail_id
                                    ,pld.paper_lend_id
                                    ,pld.paper_basic_id
                                    ,pld.paper_demand_id
                                    ,vg.contract_number
                                    ,vg.contract_name
                                    ,pld.paper_code
                                    ,pld.paper_name
                                    ,pld.is_return
                                    ,pld.should_return_time
                                    ,pld.actual_return_time
                                    ,pld.receive_status
                                    ,pld.pickup_status
                                    ,pld.pickup_time
                                    ,pld.consignment_number
                                    ,pld.pickup_emplid
                                    ,pld.actual_pickup_time
                                    ,pld.overdue_day
                                    ,pld.actual_borrow_days
                                    ,pld.loan_due_date
                                    ,pld.create_user
                                    ,pld.create_time
                                    ,pld.modify_user
                                    ,pld.modify_time
                                    ,pld.is_pickup_lend
                                    ,pld.lend_return_status 
                            FROM paper_lending_detail pld
                            LEFT JOIN paper_basic_data pbd ON pld.paper_basic_id = pbd.basic_id
							LEFT JOIN paper_application_data pad ON pad.application_id = pbd.paper_applica_id
                            LEFT JOIN V_GetUnConfirmedApplication vg ON vg.apply_number = pad.apply_number
                            WHERE pld.paper_lend_id = @paper_lend_id";
            return NpgsqlSearchByList<paper_lending_detail>(sql, new { paper_lend_id });
        }

        /// <summary>
        /// 查詢轉單部分郵件相關人
        /// </summary>
        /// <param name="list">list</param>
        /// <returns></returns>
        internal TransferEmpInfoModel QuaryTransferMailInfo(string transfer_pic_number)
        {
            string sql = @"select tpm.transfer_pic_number ,tpm.fill_emplid,tpm.handover_emplid,og.manager_id as manager_emplid,
                           operator.emplid
                    from transfer_pic_main tpm
                    left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a)fee on tpm.fill_emplid = fee.emplid
                    left join (select emplid,deptid from ps_sub_ee_lgl_vw_a)pic on pic.emplid = tpm.apply_emplid 
                    left join (select manager_id,deptid from ps_sub_og_lgl_vw_a)og on og.deptid = pic.deptid
                    left join (select emplid,name,name_a,email_address_a from ps_sub_ee_lgl_vw_a)operator on operator.emplid = COALESCE (tpm.modify_user,tpm.create_user)
                    where transfer_pic_number = @transfer_pic_number";
            return NpgsqlSearchBySingle<TransferEmpInfoModel>(sql.ToString(), new { transfer_pic_number = transfer_pic_number });
        }

        /// <summary>
        /// 根據 func_module 獲取當前郵件的隱嗎字段
        /// </summary>
        /// <param name="func_module"> user_email_dictionary表Field_type 字段，也是user_email_content表 func_module字段</param>
        /// <returns></returns>
        public List<hidden_code_dictionary> GetHiddenCodeByEmailFuncModule(string func_module) {
            string sql = @$"select * from  hidden_code_dictionary hcd 
                        WHERE  EXISTS (select 1 from user_email_dictionary ued where ued.field_code=hcd.hidden_code  and ued.Field_type='{func_module}' )
                        and hidden_type ='mail'
                        and is_used='1'";
            return NpgsqlSearchByList<hidden_code_dictionary>(sql);
        }
    }
}
