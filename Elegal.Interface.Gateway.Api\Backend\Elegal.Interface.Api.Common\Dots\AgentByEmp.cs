namespace Elegal.Interface.Api.Common.Dots
{
    /// <summary>
    /// 代理人表
    /// </summary>
    public class AgentByEmp
    {
        /// <summary>
        /// 被授權人工号
        /// </summary>
        public string agent_empid { get; set; } = string.Empty;

        /// <summary>
        /// 被授權人部門
        /// </summary>
        public string agent_deptid { get; set; } = string.Empty;

        /// <summary>
        /// 授權人工号
        /// </summary>
        public string auth_empid { get; set; } = string.Empty;

        /// <summary>
        /// 授權人部門
        /// </summary>
        public string auth_deptid { get; set; } = string.Empty;

        /// <summary>
        /// 代理開始時間
        /// </summary>
        public DateTime? start_time { get; set; }

        /// <summary>
        /// 代理結束時間
        /// </summary>
        public DateTime? end_time { get; set; }


    }
}
