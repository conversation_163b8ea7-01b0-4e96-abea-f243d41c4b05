﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///首頁區塊顯示狀態對照表
    ///</summary>
    [SugarTable("sys_home_block_status")]
    public partial class sys_home_block_status
    {
           public sys_home_block_status(){


           }
           /// <summary>
           /// Desc:序號；主鍵自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string emplid {get;set;} = null!;

           /// <summary>
           /// Desc:區塊ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int block_id {get;set;}

           /// <summary>
           /// Desc:顯示狀態，1：顯示；0：關閉
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool status {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:更新者
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:更新時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
