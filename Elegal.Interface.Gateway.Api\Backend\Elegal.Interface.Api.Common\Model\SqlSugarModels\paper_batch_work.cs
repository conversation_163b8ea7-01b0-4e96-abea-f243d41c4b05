﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///紙本批次作業
    ///</summary>
    [SugarTable("paper_batch_work")]
    public partial class paper_batch_work
    {
           public paper_batch_work(){


           }
           /// <summary>
           /// Desc:主鍵，序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int batchid {get;set;}

           /// <summary>
           /// Desc:申請單號，規則：P+西元年+5碼流水號(次年重新計算)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string batch_number {get;set;} = null!;

           /// <summary>
           /// Desc:紙本id，paper_basic_data.basic_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_basic_id {get;set;}

           /// <summary>
           /// Desc:紙本編號，唯一，由申請單號自動排序(ex：C202300233-01)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_code {get;set;} = null!;

           /// <summary>
           /// Desc:紙本名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_name {get;set;} = null!;

           /// <summary>
           /// Desc:合約編號，對應申請單的合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:合約名稱，對應申請單的合約名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_name {get;set;}

           /// <summary>
           /// Desc:批次修改後的存放位置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? batch_position {get;set;}

           /// <summary>
           /// Desc:對應每個紙本id的存放位置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_position {get;set;}

           /// <summary>
           /// Desc:批次入庫狀態，參數表 para_code = lib_paperEntryStatus
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? batch_entry_status {get;set;}

           /// <summary>
           /// Desc:入庫狀態，參數表 para_code = lib_paperEntryStatus
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_entry_status {get;set;}

           /// <summary>
           /// Desc:批次銷毀日期，當入庫狀態選擇已銷毀時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? batch_destory_time {get;set;}

           /// <summary>
           /// Desc:(暫時不用)銷毀日期，當入庫狀態選擇已銷毀時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? destroy_time {get;set;}

           /// <summary>
           /// Desc:批次遺失日期，當入庫狀態選擇已遺失時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? batch_lost_time {get;set;}

           /// <summary>
           /// Desc:(暫時不用)遺失日期，當入庫狀態選擇已遺失時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? lost_time {get;set;}

           /// <summary>
           /// Desc:批次備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? batch_remarks {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:批次銷毀原因，當入庫狀態選擇已銷毀時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? batch_destroy_reason {get;set;}

           /// <summary>
           /// Desc:批次遺失原因，當入庫狀態選擇已遺失時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? batch_lost_reason {get;set;}

    }
}
