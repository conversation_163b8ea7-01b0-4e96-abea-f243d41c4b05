﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///系統關站設定對照表
    ///</summary>
    [SugarTable("sys_shutdown")]
    public partial class sys_shutdown
    {
           public sys_shutdown(){


           }
           /// <summary>
           /// Desc:序號；主鍵自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:開始日期
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime start_date {get;set;}

           /// <summary>
           /// Desc:開始時間
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime start_time {get;set;}

           /// <summary>
           /// Desc:結束日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? end_date {get;set;}

           /// <summary>
           /// Desc:結束時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? end_time {get;set;}

           /// <summary>
           /// Desc:系統關站狀態(sys_parameters.para_code = 'shutdownStatus')
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int shutdown_status {get;set;}

           /// <summary>
           /// Desc:關站提示語
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string shutdown_content {get;set;} = null!;

           /// <summary>
           /// Desc:公告信息(fnp_new.newid)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int new_id {get;set;}

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
