﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///用戶操作日誌記錄
    ///</summary>
    [SugarTable("log_record")]
    public partial class log_record
    {
           public log_record(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public Guid logid {get;set;}

           /// <summary>
           /// Desc:操作id，對應log_record_dictionary.control_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string control_id {get;set;} = null!;

           /// <summary>
           /// Desc:操作名稱，對應log_record_dictionary.control_name
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string control_name {get;set;} = null!;

           /// <summary>
           /// Desc:記錄內容，當log_record_dictionary.is_record_first=1時，不可為空
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? detail {get;set;}

           /// <summary>
           /// Desc:操作前內容，當log_record_dictionary.is_record_first=1時，不可為空
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? detail_former {get;set;}

           /// <summary>
           /// Desc:登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string emplid_login {get;set;} = null!;

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string emplid_current {get;set;} = null!;

           /// <summary>
           /// Desc:記錄時間
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime log_time {get;set;}

           /// <summary>
           /// Desc:記錄來源，1：行動簽核(MCP)，2：WEB簽核(Web)，3：WiSign(WiSign)
           /// Default:2
           /// Nullable:False
           /// </summary>           
           public int tag_from {get;set;}

           /// <summary>
           /// Desc:主機名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? host_name {get;set;}

    }
}
