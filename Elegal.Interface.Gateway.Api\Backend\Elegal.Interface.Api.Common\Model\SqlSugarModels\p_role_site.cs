﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///角色區域對照表
    ///</summary>
    [SugarTable("p_role_site")]
    public partial class p_role_site
    {
           public p_role_site(){


           }
           /// <summary>
           /// Desc:序號，主鍵自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int fnid {get;set;}

           /// <summary>
           /// Desc:角色id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int r_id {get;set;}

           /// <summary>
           /// Desc:父類id；默認為3
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int p_r_id {get;set;}

           /// <summary>
           /// Desc:工作所在地，對應(ps_sub_loc_vw_a.site_id_a)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string sal_location_a {get;set;} = null!;

    }
}
