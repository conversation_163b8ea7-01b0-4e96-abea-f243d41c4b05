﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///菜單按鈕對照表(模板)
    ///</summary>
    [SugarTable("sys_menu_action")]
    public partial class sys_menu_action
    {
           public sys_menu_action(){


           }
           /// <summary>
           /// Desc:序號；自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:主鍵，菜單欄code，sys_menu.MenuCode
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public int menu_code {get;set;}

           /// <summary>
           /// Desc:按鈕權限code(對應sys_action.ActionCode)，ex：1,2,3,4
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string action_code {get;set;} = null!;

           /// <summary>
           /// Desc:是否使用；1：使用；0：禁用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int is_used {get;set;}

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:是否為特殊菜單欄按鈕權限，有值，表示會查詢參數表對應的para_code對應的值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? special_value {get;set;}

    }
}
