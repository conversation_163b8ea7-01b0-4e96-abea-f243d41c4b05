﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///組織爬層簽核人員
    ///</summary>
    [SugarTable("flow_step_signer_org")]
    public partial class flow_step_signer_org
    {
           public flow_step_signer_org(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:組織人員工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string org_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:組織人員部門
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string org_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:組織爬層層級
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int org_level {get;set;}

           /// <summary>
           /// Desc:簽核關卡
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int step_id {get;set;}

           /// <summary>
           /// Desc:組織爬層類型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? org_type {get;set;}

           /// <summary>
           /// Desc:是否已經執行；1：執行；0：未執行
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_used {get;set;}

    }
}
