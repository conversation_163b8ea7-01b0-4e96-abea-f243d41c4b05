﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///人員信息表
    ///</summary>
    [SugarTable("ps_sub_ee_lgl_vw_a")]
    public partial class ps_sub_ee_lgl_vw_a
    {
           public ps_sub_ee_lgl_vw_a(){


           }
           /// <summary>
           /// Desc:員工工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string emplid {get;set;} = null!;

           /// <summary>
           /// Desc:員工姓名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;} = null!;

           /// <summary>
           /// Desc:員工英文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name_a {get;set;} = null!;

           /// <summary>
           /// Desc:所屬主管工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string supervisor_id {get;set;} = null!;

           /// <summary>
           /// Desc:支薪地，可能用不到
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string sal_location_a {get;set;} = null!;

           /// <summary>
           /// Desc:所屬地代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string location {get;set;} = null!;

           /// <summary>
           /// Desc:工作所在地
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? site_id_a {get;set;}

           /// <summary>
           /// Desc:所屬部門代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:離職時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? termination {get;set;}

           /// <summary>
           /// Desc:所屬公司代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company {get;set;} = null!;

           /// <summary>
           /// Desc:電郵地址
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string email_address_a {get;set;} = null!;

           /// <summary>
           /// Desc:分機號碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string phone_a {get;set;} = null!;

           /// <summary>
           /// Desc:所屬地說明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? descr_loc_a {get;set;}

           /// <summary>
           /// Desc:所屬部門說明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? descr50 {get;set;}

           /// <summary>
           /// Desc:所屬公司名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? descr40 {get;set;}

           /// <summary>
           /// Desc:電話區碼
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? prefix_dial_code_a {get;set;}

           /// <summary>
           /// Desc:事業群
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? bg {get;set;}

           /// <summary>
           /// Desc:BO\BU 業務單位
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? bu {get;set;}

           /// <summary>
           /// Desc:座位區域
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? desk_location {get;set;}

    }
}
