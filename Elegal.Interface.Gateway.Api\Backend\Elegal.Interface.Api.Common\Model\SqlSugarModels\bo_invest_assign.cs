﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///長投表
    ///</summary>
    [SugarTable("bo_invest_assign")]
    public partial class bo_invest_assign
    {
           public bo_invest_assign(){


           }
           /// <summary>
           /// Desc:主體id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:主體簡稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity {get;set;} = null!;

           /// <summary>
           /// Desc:掛帳部門名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string account_dept {get;set;} = null!;

           /// <summary>
           /// Desc:會簽總經理工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string gm_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:會簽總經理名字
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string gm_name {get;set;} = null!;

           /// <summary>
           /// Desc:會簽董事長工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? cm_emplid {get;set;}

           /// <summary>
           /// Desc:會簽董事長名字
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? cm_name {get;set;}

    }
}
