﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///站點維護
    ///</summary>
    [SugarTable("sys_maintain")]
    public partial class sys_maintain
    {
           public sys_maintain(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:維護類型；0：自動；1：手動
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int maintain_type {get;set;}

           /// <summary>
           /// Desc:維護狀態；0：停用；1：啟用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public bool maintain_status {get;set;}

           /// <summary>
           /// Desc:維護提示語
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? maintain_content {get;set;}

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:系統關站操作id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? shutdown_id {get;set;}

    }
}
