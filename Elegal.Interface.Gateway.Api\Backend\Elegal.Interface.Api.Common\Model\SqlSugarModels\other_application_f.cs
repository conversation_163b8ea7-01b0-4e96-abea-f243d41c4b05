﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///其他申請 -> F)：特殊主體申請
    ///</summary>
    [SugarTable("other_application_f")]
    public partial class other_application_f
    {
           public other_application_f(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public long rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與 other_application.apply_number 對應
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:特殊主體
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? special_entity_id {get;set;}

           /// <summary>
           /// Desc:特殊申請類別；sys_parameters.para_code = N'specialType'
           /// Default:01
           /// Nullable:True
           /// </summary>           
           public string? special_type {get;set;}

           /// <summary>
           /// Desc:申請原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_reason {get;set;}

           /// <summary>
           /// Desc:申請開放期間(起日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_date_start {get;set;}

           /// <summary>
           /// Desc:申請開放期間(迄日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_date_end {get;set;}

           /// <summary>
           /// Desc:其他說明/備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_remark {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:申請開放日期差值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? apply_open_time {get;set;}

    }
}
