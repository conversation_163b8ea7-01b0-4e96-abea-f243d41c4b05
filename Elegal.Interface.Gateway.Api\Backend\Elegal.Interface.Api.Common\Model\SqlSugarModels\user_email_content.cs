﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///user -> 郵件模板管理
    ///</summary>
    [SugarTable("user_email_content")]
    public partial class user_email_content
    {
           public user_email_content(){


           }
           /// <summary>
           /// Desc:序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:功能模組；sys_parameters.para_code = N'e_funModule'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string func_module {get;set;} = null!;

           /// <summary>
           /// Desc:郵件類型；sys_parameters.para_code = N'e_mailType'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string mail_type {get;set;} = null!;

           /// <summary>
           /// Desc:發送模式；sys_parameters.para_code = N'e_sendMode'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string send_mode {get;set;} = null!;

           /// <summary>
           /// Desc:郵件主旨
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string mail_subject {get;set;} = null!;

           /// <summary>
           /// Desc:郵件收件者類型，可多選
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string mail_re_type {get;set;} = null!;

           /// <summary>
           /// Desc:郵件抄送者類型，可多選
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? mail_cc_type {get;set;}

           /// <summary>
           /// Desc:郵件中文模板
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string mail_zh_content {get;set;} = null!;

           /// <summary>
           /// Desc:郵件英文模板
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? mail_en_content {get;set;}

           /// <summary>
           /// Desc:郵件模板，由中文模板+英文模板為完整的郵件模板
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string mail_content {get;set;} = null!;

           /// <summary>
           /// Desc:提前天數
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? advance_days {get;set;}

           /// <summary>
           /// Desc:間隔天數
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? interval_days {get;set;}

           /// <summary>
           /// Desc:郵件備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? mail_reamrk {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:system
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；操作者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:收件者是否需要發送給代理人；0：否；1：是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_re_agent {get;set;}

           /// <summary>
           /// Desc:是否有表格內容；0：否；1：是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool having_table {get;set;}

           /// <summary>
           /// Desc:郵件中存在table屬性時，需要將table轉換為json格式，以便後端處理
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? table_json {get;set;}

           /// <summary>
           /// Desc:郵件收件者/CC人員在參數表中對應的para_code，多個以 ; 拼接
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? mail_rc_code {get;set;}

           /// <summary>
           /// Desc:中文模板json
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? zh_content_json {get;set;}

           /// <summary>
           /// Desc:英文模板json
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? en_content_json {get;set;}

           /// <summary>
           /// Desc:是否為超鏈接郵件，	0：不是，即時郵件內容中包含需要超鏈接的欄位也不會存在可點擊的超鏈接；	1：是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_linkmail {get;set;}

           /// <summary>
           /// Desc:是否需要對郵件中欄位進行隱碼處理；0：不需要，1：需要
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_hidden_code {get;set;}

    }
}
