﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///api系統日誌
    ///</summary>
    [SugarTable("sys_api_log")]
    public partial class sys_api_log
    {
           public sys_api_log(){


           }
           /// <summary>
           /// Desc:主鍵id，序號自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int logid {get;set;}

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string emp_id {get;set;} = null!;

           /// <summary>
           /// Desc:api名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string api_name {get;set;} = null!;

           /// <summary>
           /// Desc:呼叫控制器名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string controller_name {get;set;} = null!;

           /// <summary>
           /// Desc:呼叫路徑
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string call_path {get;set;} = null!;

           /// <summary>
           /// Desc:呼叫狀態碼。"99999"系統默認，無狀態
           /// Default:99999
           /// Nullable:False
           /// </summary>           
           public string call_result {get;set;} = null!;

           /// <summary>
           /// Desc:呼叫類型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string call_method {get;set;} = null!;

           /// <summary>
           /// Desc:詳細信息
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? log_detial {get;set;}

           /// <summary>
           /// Desc:呼叫時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
