﻿using System.Text;
using Dapper;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel.FlowStep;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Orm;

namespace Elegal.Flow.Common.Repository.FlowStep
{
    public class FlowStepProcessRepository : BaseRepository
    {
        #region 事務處理

        #region 獲取完整簽核流程
        /// <summary>
        /// 獲取完整簽核流程
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_type"></param>
        /// <param name="form_type"></param>
        /// <param name="apply_number"></param>
        /// <param name="flowStepID"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<ApplicationApproveProcess> GetApplicationApproveBrforeFlowToTransaction(IDbContext dbContext, string apply_type, string form_type, string apply_number, int? flowStepID = null)
        {
            List<ApplicationApproveProcess> result = new List<ApplicationApproveProcess>();

            string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
            if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType, @flowStepID";
            result = this.NpgsqlSearchByListToTransaction<ApplicationApproveProcess>(sql, dbContext, new
            {
                lang = MvcContext.UserInfo.logging_locale,
                apply_number = apply_number,
                applicationType = apply_type,
                flowStepID = flowStepID
            });

            if (result != null && result.Count > 0)
            {
                //插入簽核流程前去重
                result = result.Distinct().ToList();

                foreach (ApplicationApproveProcess item in result)
                {
                    if (!string.IsNullOrEmpty(item.signerQuitTime))
                    {
                        item.signerQuitTimeTrans = DateTime.Parse(item.signerQuitTime);
                    }
                }
            }

            return result;
        }
        #endregion

        #region 修改申請單簽核人員
        /// <summary>
        /// 修改申請單簽核人員
        /// </summary>
        /// <param name="apply_type"></param>
        /// <param name="form_type"></param>
        /// <param name="apply_number"></param>
        /// <param name="updateStepID"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public bool UpdateApplicationApproveToTransaction(string apply_type, string form_type, string apply_number, int updateStepID, IDbContext dbContext)
        {
            FlowStepRepository _stepRepository = new FlowStepRepository();
            //int stepID = _stepRepository.GetWhqLowProStepid(flow_id);

            //檢查是否已有完整簽核流程，如沒有則不可更改
            List<flow_step_process> oldStepList = GetFlowStepProcessListToTransaction(apply_type, form_type, apply_number, dbContext);
            if (oldStepList == null || oldStepList.Count == 0) return true;

            //申請單如沒有對應關卡則直接返回
            if (updateStepID == null) return true;

            //刪除原有簽核流程
            DeleteFlowStepProcessByStepToTransaction(apply_number, updateStepID, dbContext);

            string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
            if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType,  @flowStepID";
            List<ApplicationApproveProcess> flowList = this.NpgsqlSearchByListToTransaction<ApplicationApproveProcess>(sql, dbContext, new
            {
                lang = MvcContext.UserInfo.logging_locale,
                apply_number = apply_number,
                applicationType = apply_type,
                flowStepID = updateStepID
            });

            List<flow_step_process> ackList = new List<flow_step_process>();
            if (flowList != null && flowList.Count > 0)
            {
                //插入簽核流程前去重
                flowList = flowList.Distinct().ToList();

                foreach (var item in flowList)
                {
                    flow_step_process process = new flow_step_process();
                    process.apply_number = apply_number;
                    process.apply_type = apply_type;
                    process.form_type = form_type;
                    process.step_id = item.stepID;
                    process.signer_emplid = item.signerEmplid;
                    process.signer_deptid = item.signerDeptid;
                    process.dept_level = item.deptLevel;
                    process.signer_c_name = item.signerCName;
                    process.signer_e_name = item.signerEName;
                    if (!string.IsNullOrEmpty(item.signerQuitTime))
                    {
                        process.signer_quit_time = DateTime.Parse(item.signerQuitTime);
                    }
                    process.current_e_name = item.funEname;
                    process.current_c_name = item.funName;
                    process.step_name = item.stepName;
                    process.next_step_id = item.nextStepID;
                    process.step_type = Convert.ToInt16(item.stepType);
                    process.approval_type = Convert.ToInt16(item.approvalType);
                    process.approve_level = item.approveLevel;
                    process.ac_type_level = item.acTypeLevel;
                    process.ac_type = string.IsNullOrEmpty(item.acType) ? null : Convert.ToInt16(item.acType);
                    process.ac_type_e_name = item.acTypeEName;
                    process.ac_type_c_name = item.acTypeName;
                    process.ac_step = string.IsNullOrEmpty(item.acStep) ? null : Convert.ToInt16(item.acStep);
                    process.ac_step_level = item.acStepLevel;
                    process.ac_step_e_name = item.acStepEName;
                    process.ac_step_c_name = item.acStepName;
                    process.is_dynamic_step = item.isDynamicStep;
                    process.is_acknowledge = item.isAcknowledge;
                    process.create_user = MvcContext.UserInfo.current_emp;
                    process.create_time = DateTime.UtcNow;

                    ackList.Add(process);
                }

                InsertFlowStepProcessToTransaction(ackList, dbContext);
            }

            return true;
        }
        #endregion

        #region 獲取簽核流程
        /// <summary>
        /// 獲取簽核流程
        /// </summary>
        /// <param name="apply_type"></param>
        /// <param name="form_type"></param>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public List<flow_step_process> GetFlowStepProcessListToTransaction(string apply_type, string form_type, string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT
                                apply_number,
                                apply_type,
                                form_type,
                                step_id,
                                signer_emplid,
                                signer_deptid,
                                dept_level,
                                signer_c_name,
                                signer_e_name,
                                signer_quit_time,
                                current_e_name,
                                current_c_name,
                                step_name,
                                next_step_id,
                                step_type,
                                approval_type,
                                approve_level,
                                ac_type_level,
                                ac_type,
                                ac_type_e_name,
                                ac_type_c_name,
                                ac_step,
                                ac_step_level,
                                ac_step_e_name,
                                ac_step_c_name,
                                is_dynamic_step,
                                is_acknowledge,
                                create_user,
                                create_time
                            FROM flow_step_process
                            WHERE apply_number = @apply_number
                                AND apply_type = @apply_type
                                AND form_type = @form_type
                            ORDER BY step_id ASC";
            return NpgsqlSearchByListToTransaction<flow_step_process>(sql, dbContext, new { apply_number, apply_type, form_type });
        }
        #endregion

        #region 根據StepID刪除特定關卡簽核人員
        /// <summary>
        /// 根據StepID刪除特定關卡簽核人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="step_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool DeleteFlowStepProcessByStepToTransaction(string apply_number, int step_id, IDbContext dbContext)
        {
            string sql = $@"DELETE FROM flow_step_process WHERE apply_number = @apply_number AND step_id = @step_id";
            return ExecuteCommandToTransaction(sql, dbContext, new { apply_number, step_id }) > 0;
        }
        #endregion

        #region 插入簽核人員
        /// <summary>
        /// 插入簽核人員
        /// </summary>
        /// <param name="data"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertFlowStepProcessToTransaction(List<flow_step_process> data, IDbContext dbContext)
        {
            string sql = $@"INSERT INTO flow_step_process (
                                apply_number,apply_type,form_type,step_id,signer_emplid,signer_deptid,dept_level,
                                signer_c_name,signer_e_name,signer_quit_time,current_e_name,current_c_name,
                                step_name,next_step_id,step_type,approval_type,approve_level,
                                ac_type_level,ac_type,ac_type_e_name,ac_type_c_name,
                                ac_step,ac_step_level,ac_step_e_name,ac_step_c_name,
                                is_dynamic_step,is_acknowledge,create_user,create_time
                            ) VALUES ";
            var parameters = new DynamicParameters();

            for (int i = 0; i < data.Count(); i++)
            {
                var flowStepProcess = data.ElementAt(i);

                // 添加 VALUES 子句
                sql += $"(@apply_number{i}, @apply_type{i}, @form_type{i}, @step_id{i}, @signer_emplid{i}, @signer_deptid{i}, @dept_level{i}, @signer_c_name{i}, @signer_e_name{i}, @signer_quit_time{i}, @current_e_name{i}, @current_c_name{i}, @step_name{i}, @next_step_id{i}, @step_type{i}, @approval_type{i}, @approve_level{i}, @ac_type_level{i}, @ac_type{i}, @ac_type_e_name{i}, @ac_type_c_name{i}, @ac_step{i}, @ac_step_level{i}, @ac_step_e_name{i}, @ac_step_c_name{i}, @is_dynamic_step{i}, @is_acknowledge{i}, @create_user{i}, @create_time{i})";
                if (i < data.Count() - 1)
                {
                    sql += ", ";
                }
                // 添加参数到 DynamicParameters
                parameters.Add($"apply_number{i}", flowStepProcess.apply_number);
                parameters.Add($"apply_type{i}", flowStepProcess.apply_type);
                parameters.Add($"form_type{i}", flowStepProcess.form_type);
                parameters.Add($"step_id{i}", flowStepProcess.step_id);
                parameters.Add($"signer_emplid{i}", flowStepProcess.signer_emplid);
                parameters.Add($"signer_deptid{i}", flowStepProcess.signer_deptid);
                parameters.Add($"dept_level{i}", flowStepProcess.dept_level);
                parameters.Add($"signer_c_name{i}", flowStepProcess.signer_c_name);
                parameters.Add($"signer_e_name{i}", flowStepProcess.signer_e_name);
                parameters.Add($"signer_quit_time{i}", flowStepProcess.signer_quit_time);
                parameters.Add($"current_e_name{i}", flowStepProcess.current_e_name);
                parameters.Add($"current_c_name{i}", flowStepProcess.current_c_name);
                parameters.Add($"step_name{i}", flowStepProcess.step_name);
                parameters.Add($"next_step_id{i}", flowStepProcess.next_step_id);
                parameters.Add($"step_type{i}", flowStepProcess.step_type);
                parameters.Add($"approval_type{i}", flowStepProcess.approval_type);
                parameters.Add($"approve_level{i}", flowStepProcess.approve_level);
                parameters.Add($"ac_type_level{i}", flowStepProcess.ac_type_level);
                parameters.Add($"ac_type{i}", flowStepProcess.ac_type);
                parameters.Add($"ac_type_e_name{i}", flowStepProcess.ac_type_e_name);
                parameters.Add($"ac_type_c_name{i}", flowStepProcess.ac_type_c_name);
                parameters.Add($"ac_step{i}", flowStepProcess.ac_step);
                parameters.Add($"ac_step_level{i}", flowStepProcess.ac_step_level);
                parameters.Add($"ac_step_e_name{i}", flowStepProcess.ac_step_e_name);
                parameters.Add($"ac_step_c_name{i}", flowStepProcess.ac_step_c_name);
                parameters.Add($"is_dynamic_step{i}", flowStepProcess.is_dynamic_step);
                parameters.Add($"is_acknowledge{i}", flowStepProcess.is_acknowledge);
                parameters.Add($"create_user{i}", flowStepProcess.create_user);
                parameters.Add($"create_time{i}", flowStepProcess.create_time);
            }
            return ExecuteCommandToTransaction(sql, dbContext, parameters) > 0;
        }
        #endregion

        #region 獲取申請單簽核流程
        /// <summary>
        /// 獲取申請單簽核流程
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_type"></param>
        /// <param name="form_type"></param>
        /// <param name="apply_number"></param>
        /// <param name="flow_step"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<ApplicationApproveProcess> GetApplicationApproveToTransaction(IDbContext dbContext, string apply_type, string form_type, string apply_number, int? flow_step = null)
        {
            //Issue：103
            apply_type = PublicHelper.GetApplicationType(apply_type);

            List<flow_step_process> res = GetFlowStepProcessListToTransaction(apply_type, form_type, apply_number, dbContext);

            if (res == null || res.Count == 0)
            {
                string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
                if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

                string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType";
                List<ApplicationApproveProcess> flowList = this.NpgsqlSearchByListToTransaction<ApplicationApproveProcess>(sql, dbContext, new
                {
                    lang = MvcContext.UserInfo.logging_locale,
                    apply_number = apply_number,
                    applicationType = apply_type
                });

                if (flowList != null && flowList.Count > 0)
                {
                    //插入簽核流程前去重
                    flowList = flowList.Distinct().ToList();

                    foreach (var item in flowList)
                    {
                        flow_step_process process = new flow_step_process();
                        process.apply_number = apply_number;
                        process.apply_type = apply_type;
                        process.form_type = form_type;
                        process.step_id = item.stepID;
                        process.signer_emplid = item.signerEmplid;
                        process.signer_deptid = item.signerDeptid;
                        process.dept_level = item.deptLevel;
                        process.signer_c_name = item.signerCName;
                        process.signer_e_name = item.signerEName;
                        if (!string.IsNullOrEmpty(item.signerQuitTime))
                        {
                            process.signer_quit_time = DateTime.Parse(item.signerQuitTime);
                        }
                        process.current_e_name = item.funEname;
                        process.current_c_name = item.funName;
                        process.step_name = item.stepName;
                        process.next_step_id = item.nextStepID;
                        process.step_type = Convert.ToInt16(item.stepType);
                        process.approval_type = Convert.ToInt16(item.approvalType);
                        process.approve_level = item.approveLevel;
                        process.ac_type_level = item.acTypeLevel;
                        process.ac_type = string.IsNullOrEmpty(item.acType) ? null : Convert.ToInt16(item.acType);
                        process.ac_type_e_name = item.acTypeEName;
                        process.ac_type_c_name = item.acTypeName;
                        process.ac_step = string.IsNullOrEmpty(item.acStep) ? null : Convert.ToInt16(item.acStep);
                        process.ac_step_level = item.acStepLevel;
                        process.ac_step_e_name = item.acStepEName;
                        process.ac_step_c_name = item.acStepName;
                        process.is_dynamic_step = item.isDynamicStep;
                        process.is_acknowledge = item.isAcknowledge;
                        process.create_user = MvcContext.UserInfo.current_emp;
                        process.create_time = DateTime.UtcNow;

                        res.Add(process);
                    }

                    InsertFlowStepProcessToTransaction(res, dbContext);
                }
                else
                {
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:StepSignerError"));
                }
            }

            List<ApplicationApproveProcess> result = new List<ApplicationApproveProcess>();
            if (res != null && res.Count > 0)
            {
                if (flow_step != null) res = res.Where(x => x.step_id == flow_step).ToList();

                foreach (flow_step_process process in res)
                {
                    ApplicationApproveProcess app = new ApplicationApproveProcess();
                    app.stepID = process.step_id;
                    app.signerEmplid = process.signer_emplid;
                    app.signerDeptid = process.signer_deptid;
                    app.deptLevel = process.dept_level;
                    app.signerCName = process.signer_c_name;
                    app.signerEName = process.signer_e_name;
                    app.signerQuitTime = process.signer_quit_time?.ToString("yyyy/MM/dd HH:mm:ss");
                    app.signerQuitTimeTrans = process.signer_quit_time;
                    app.funName = MvcContext.UserInfo.logging_locale == "EN-US" ? process.current_e_name : process.current_c_name;
                    app.stepName = process.step_name;
                    app.nextStepID = process.next_step_id;
                    app.stepType = process.step_type.ToString();
                    app.approvalType = process.approval_type.ToString();
                    app.approveLevel = process.approve_level;
                    app.acTypeLevel = process.ac_type_level;
                    app.acType = process.ac_type?.ToString();
                    app.acTypeName = MvcContext.UserInfo.logging_locale == "ZH-TW" ? process.ac_type_c_name : process.ac_type_e_name;
                    app.acStep = process.ac_step?.ToString();
                    app.acStepLevel = process.ac_step_level;
                    app.acStepName = MvcContext.UserInfo.logging_locale == "ZH-TW" ? process.ac_step_c_name : process.ac_step_e_name;
                    app.isDynamicStep = process.is_dynamic_step;
                    app.isAcknowledge = process.is_acknowledge;
                    result.Add(app);
                }
            }
            return result;
        }
        #endregion

        #region 根據申請單號刪除簽核人員
        /// <summary>
        /// 根據申請單號刪除簽核人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool DeleteFlowStepProcessToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"DELETE FROM flow_step_process WHERE apply_number = @apply_number";
            return ExecuteCommandToTransaction(sql, dbContext, new { apply_number }) > 0;
        }
        #endregion

        #region 獲取駁回或抽單後最新的簽核歷程
        /// <summary>
        /// 獲取駁回或抽單後最新的簽核歷程
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<ApplicationApproveProcess> GetApplicationFinaSignerListToTransaction(string apply_number, IDbContext dbContext)
        {
            return this.NpgsqlSearchByListToTransaction<ApplicationApproveProcess>(@"SELECT step_id AS stepID,actual_signer_emplid AS signerEmplid,step_name AS stepName FROM dbo.V_GetFinallySignerHistory WHERE step_action <> -3 AND step_action <> -2 AND apply_number = @apply_number;", dbContext, new
            {
                apply_number = apply_number
            });
        }
        #endregion

        #region 檢查簽核人員是否在職
        /// <summary>
        /// 檢查簽核人員是否在職
        /// </summary>
        /// <param name="signerEmplids"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<string> GetSignerByOnJobToTransaction(List<string> signerEmplids, IDbContext dbContext)
        {
            string getSignerByOnJobSql = @"
                   SELECT ee.emplid FROM (SELECT emplid FROM dbo.ps_sub_ee_lgl_vw_a WHERE ISNULL(termination,N'') = N'') AS ee
                   INNER JOIN (SELECT value AS emplid FROM STRING_SPLIT(@signerEmplids, ',')) AS se ON se.emplid = ee.emplid;";
            return this.NpgsqlSearchByListToTransaction<string>(getSignerByOnJobSql, dbContext, new { signerEmplids = string.Join(",", signerEmplids) });
        }
        #endregion

        #endregion

        #region FlowStepProcess
        public List<flow_step_process> GetFlowStepProcessList(string apply_type, string form_type, string apply_number)
        {
            string sql = $@"SELECT
                                apply_number,
                                apply_type,
                                form_type,
                                step_id,
                                signer_emplid,
                                signer_deptid,
                                dept_level,
                                signer_c_name,
                                signer_e_name,
                                signer_quit_time,
                                current_e_name,
                                current_c_name,
                                step_name,
                                next_step_id,
                                step_type,
                                approval_type,
                                approve_level,
                                ac_type_level,
                                ac_type,
                                ac_type_e_name,
                                ac_type_c_name,
                                ac_step,
                                ac_step_level,
                                ac_step_e_name,
                                ac_step_c_name,
                                is_dynamic_step,
                                is_acknowledge,
                                create_user,
                                create_time
                            FROM flow_step_process
                            WHERE apply_number = @apply_number
                                AND apply_type = @apply_type
                                AND form_type = @form_type
                            ORDER BY step_id ASC";
            return NpgsqlSearchByList<flow_step_process>(sql, new { apply_number, apply_type, form_type });
        }

        public bool InsertFlowStepProcess(List<flow_step_process> data, IDbContext? dbContext = null)
        {
            string sql = $@"INSERT INTO flow_step_process (
                                apply_number,apply_type,form_type,step_id,signer_emplid,signer_deptid,dept_level,
                                signer_c_name,signer_e_name,signer_quit_time,current_e_name,current_c_name,
                                step_name,next_step_id,step_type,approval_type,approve_level,
                                ac_type_level,ac_type,ac_type_e_name,ac_type_c_name,
                                ac_step,ac_step_level,ac_step_e_name,ac_step_c_name,
                                is_dynamic_step,is_acknowledge,create_user,create_time
                            ) VALUES ";
            var parameters = new DynamicParameters();

            for (int i = 0; i < data.Count(); i++)
            {
                var flowStepProcess = data.ElementAt(i);

                // 添加 VALUES 子句
                sql += $"(@apply_number{i}, @apply_type{i}, @form_type{i}, @step_id{i}, @signer_emplid{i}, @signer_deptid{i}, @dept_level{i}, @signer_c_name{i}, @signer_e_name{i}, @signer_quit_time{i}, @current_e_name{i}, @current_c_name{i}, @step_name{i}, @next_step_id{i}, @step_type{i}, @approval_type{i}, @approve_level{i}, @ac_type_level{i}, @ac_type{i}, @ac_type_e_name{i}, @ac_type_c_name{i}, @ac_step{i}, @ac_step_level{i}, @ac_step_e_name{i}, @ac_step_c_name{i}, @is_dynamic_step{i}, @is_acknowledge{i}, @create_user{i}, @create_time{i})";
                if (i < data.Count() - 1)
                {
                    sql += ", ";
                }
                // 添加参数到 DynamicParameters
                parameters.Add($"apply_number{i}", flowStepProcess.apply_number);
                parameters.Add($"apply_type{i}", flowStepProcess.apply_type);
                parameters.Add($"form_type{i}", flowStepProcess.form_type);
                parameters.Add($"step_id{i}", flowStepProcess.step_id);
                parameters.Add($"signer_emplid{i}", flowStepProcess.signer_emplid);
                parameters.Add($"signer_deptid{i}", flowStepProcess.signer_deptid);
                parameters.Add($"dept_level{i}", flowStepProcess.dept_level);
                parameters.Add($"signer_c_name{i}", flowStepProcess.signer_c_name);
                parameters.Add($"signer_e_name{i}", flowStepProcess.signer_e_name);
                parameters.Add($"signer_quit_time{i}", flowStepProcess.signer_quit_time);
                parameters.Add($"current_e_name{i}", flowStepProcess.current_e_name);
                parameters.Add($"current_c_name{i}", flowStepProcess.current_c_name);
                parameters.Add($"step_name{i}", flowStepProcess.step_name);
                parameters.Add($"next_step_id{i}", flowStepProcess.next_step_id);
                parameters.Add($"step_type{i}", flowStepProcess.step_type);
                parameters.Add($"approval_type{i}", flowStepProcess.approval_type);
                parameters.Add($"approve_level{i}", flowStepProcess.approve_level);
                parameters.Add($"ac_type_level{i}", flowStepProcess.ac_type_level);
                parameters.Add($"ac_type{i}", flowStepProcess.ac_type);
                parameters.Add($"ac_type_e_name{i}", flowStepProcess.ac_type_e_name);
                parameters.Add($"ac_type_c_name{i}", flowStepProcess.ac_type_c_name);
                parameters.Add($"ac_step{i}", flowStepProcess.ac_step);
                parameters.Add($"ac_step_level{i}", flowStepProcess.ac_step_level);
                parameters.Add($"ac_step_e_name{i}", flowStepProcess.ac_step_e_name);
                parameters.Add($"ac_step_c_name{i}", flowStepProcess.ac_step_c_name);
                parameters.Add($"is_dynamic_step{i}", flowStepProcess.is_dynamic_step);
                parameters.Add($"is_acknowledge{i}", flowStepProcess.is_acknowledge);
                parameters.Add($"create_user{i}", flowStepProcess.create_user);
                parameters.Add($"create_time{i}", flowStepProcess.create_time);
            } 
            
            
            return ExecuteCommand(sql, parameters) > 0;
        }

        public bool DeleteFlowStepProcess(string apply_number)
        {
            string sql = $@"DELETE FROM flow_step_process WHERE apply_number = @apply_number";
            return ExecuteCommand(sql, new { apply_number }) > 0;
        }

        public bool DeleteFlowStepProcessByStep(string apply_number, int step_id, IDbContext? dbContext = null)
        {
            string sql = $@"DELETE FROM flow_step_process WHERE apply_number = @apply_number AND step_id = @step_id";
            return ExecuteCommand(sql, new { apply_number, step_id }) > 0;
        }
        #endregion

        public List<ApplicationApproveProcess> GetApplicationApproveBrforeFlow(string apply_type, string form_type, string apply_number, int? flowStepID = null)
        {
            List<ApplicationApproveProcess> result = new List<ApplicationApproveProcess>();

            string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
            if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType, @flowStepID";
            result = this.NpgsqlSearchByList<ApplicationApproveProcess>(sql, new
            {
                lang = MvcContext.UserInfo.logging_locale,
                apply_number = apply_number,
                applicationType = apply_type,
                flowStepID = flowStepID
            });

            if (result != null && result.Count > 0)
            {
                //插入簽核流程前去重
                result = result.Distinct().ToList();

                foreach (ApplicationApproveProcess item in result)
                {
                    if (!string.IsNullOrEmpty(item.signerQuitTime))
                    {
                        item.signerQuitTimeTrans = DateTime.Parse(item.signerQuitTime);
                    }
                }
            }

            return result;
        }

        public List<ApplicationApproveProcess> GetApplicationApprove(string apply_type, string form_type, string apply_number, int? flow_step = null)
        {
            //Issue：103
            apply_type = PublicHelper.GetApplicationType(apply_type);

            List<flow_step_process> res = GetFlowStepProcessList(apply_type, form_type, apply_number);

            if (res == null || res.Count == 0)
            {
                string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
                if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

                string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType";
                List<ApplicationApproveProcess> flowList = this.NpgsqlSearchByList<ApplicationApproveProcess>(sql, new
                {
                    lang = MvcContext.UserInfo.logging_locale,
                    apply_number = apply_number,
                    applicationType = apply_type
                });

                if (flowList != null && flowList.Count > 0)
                {
                    //插入簽核流程前去重
                    flowList = flowList.Distinct().ToList();

                    foreach (var item in flowList)
                    {
                        flow_step_process process = new flow_step_process();
                        process.apply_number = apply_number;
                        process.apply_type = apply_type;
                        process.form_type = form_type;
                        process.step_id = item.stepID;
                        process.signer_emplid = item.signerEmplid;
                        process.signer_deptid = item.signerDeptid;
                        process.dept_level = item.deptLevel;
                        process.signer_c_name = item.signerCName;
                        process.signer_e_name = item.signerEName;
                        if (!string.IsNullOrEmpty(item.signerQuitTime))
                        {
                            process.signer_quit_time = DateTime.Parse(item.signerQuitTime);
                        }
                        process.current_e_name = item.funEname;
                        process.current_c_name = item.funName;
                        process.step_name = item.stepName;
                        process.next_step_id = item.nextStepID;
                        process.step_type = Convert.ToInt16(item.stepType);
                        process.approval_type = Convert.ToInt16(item.approvalType);
                        process.approve_level = item.approveLevel;
                        process.ac_type_level = item.acTypeLevel;
                        process.ac_type = string.IsNullOrEmpty(item.acType) ? null : Convert.ToInt16(item.acType);
                        process.ac_type_e_name = item.acTypeEName;
                        process.ac_type_c_name = item.acTypeName;
                        process.ac_step = string.IsNullOrEmpty(item.acStep) ? null : Convert.ToInt16(item.acStep);
                        process.ac_step_level = item.acStepLevel;
                        process.ac_step_e_name = item.acStepEName;
                        process.ac_step_c_name = item.acStepName;
                        process.is_dynamic_step = item.isDynamicStep;
                        process.is_acknowledge = item.isAcknowledge;
                        process.create_user = MvcContext.UserInfo.current_emp;
                        process.create_time = DateTime.UtcNow;

                        res.Add(process);
                    }

                    InsertFlowStepProcess(res);
                }
                else
                {
                    throw new Exception(ActionFilter.GetMultilingualValue("custom:messageContent:StepSignerError"));
                }
            }

            List<ApplicationApproveProcess> result = new List<ApplicationApproveProcess>();
            if (res != null && res.Count > 0)
            {
                if (flow_step != null) res = res.Where(x => x.step_id == flow_step).ToList();

                foreach (flow_step_process process in res)
                {
                    ApplicationApproveProcess app = new ApplicationApproveProcess();
                    app.stepID = process.step_id;
                    app.signerEmplid = process.signer_emplid;
                    app.signerDeptid = process.signer_deptid;
                    app.deptLevel = process.dept_level;
                    app.signerCName = process.signer_c_name;
                    app.signerEName = process.signer_e_name;
                    app.signerQuitTime = process.signer_quit_time?.ToString("yyyy/MM/dd HH:mm:ss");
                    app.signerQuitTimeTrans = process.signer_quit_time;
                    app.funName = MvcContext.UserInfo.logging_locale == "EN-US" ? process.current_e_name : process.current_c_name;
                    app.stepName = process.step_name;
                    app.nextStepID = process.next_step_id;
                    app.stepType = process.step_type.ToString();
                    app.approvalType = process.approval_type.ToString();
                    app.approveLevel = process.approve_level;
                    app.acTypeLevel = process.ac_type_level;
                    app.acType = process.ac_type?.ToString();
                    app.acTypeName = MvcContext.UserInfo.logging_locale == "ZH-TW" ? process.ac_type_c_name : process.ac_type_e_name;
                    app.acStep = process.ac_step?.ToString();
                    app.acStepLevel = process.ac_step_level;
                    app.acStepName = MvcContext.UserInfo.logging_locale == "ZH-TW" ? process.ac_step_c_name : process.ac_step_e_name;
                    app.isDynamicStep = process.is_dynamic_step;
                    app.isAcknowledge = process.is_acknowledge;
                    result.Add(app);
                }
            }
            return result;
        }

        #region 獲取駁回或抽單後最新的簽核歷程
        /// <summary>
        /// 獲取駁回或抽單後最新的簽核歷程
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public List<ApplicationApproveProcess> GetApplicationFinaSignerList(string apply_number)
        {
            return this.NpgsqlSearchByList<ApplicationApproveProcess>(@"SELECT step_id AS stepID,actual_signer_emplid AS signerEmplid,step_name AS stepName FROM dbo.V_GetFinallySignerHistory WHERE step_action <> -3 AND step_action <> -2 AND apply_number = @apply_number;", new
            {
                apply_number = apply_number
            });
        }
        #endregion

        /// <summary>
        /// 取得簽核流程預覽(不會將SP塞進process)
        /// </summary>
        /// <param name="apply_type"></param>
        /// <param name="form_type"></param>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<ApplicationApproveProcess> GetApplicationApprovePreview(string apply_type, string form_type, string apply_number)
        {
            List<ApplicationApproveProcess> result = new List<ApplicationApproveProcess>();

            List<flow_step_process> res = GetFlowStepProcessList(apply_type, form_type, apply_number);
            if (res != null && res.Count > 0)
            {
                //簽核預覽(簽核中)時撈process
                foreach (flow_step_process process in res)
                {
                    ApplicationApproveProcess app = new ApplicationApproveProcess();
                    app.stepID = process.step_id;
                    app.signerEmplid = process.signer_emplid;
                    app.signerDeptid = process.signer_deptid;
                    app.deptLevel = process.dept_level;
                    app.signerCName = process.signer_c_name;
                    app.signerEName = process.signer_e_name;
                    app.signerQuitTime = process.signer_quit_time?.ToString("yyyy/MM/dd HH:mm:ss");
                    app.signerQuitTimeTrans = process.signer_quit_time;
                    app.funName = process.current_c_name;
                    app.funEname = process.current_e_name;
                    app.stepName = process.step_name;
                    app.nextStepID = process.next_step_id;
                    app.stepType = process.step_type.ToString();
                    app.approvalType = process.approval_type.ToString();
                    app.approveLevel = process.approve_level;
                    app.acTypeLevel = process.ac_type_level;
                    app.acType = process.ac_type?.ToString();
                    app.acTypeName = process.ac_type_c_name;
                    app.acTypeEName = process.ac_type_e_name;
                    app.acStep = process.ac_step?.ToString();
                    app.acStepLevel = process.ac_step_level;
                    app.acStepName = process.ac_step_c_name;
                    app.acStepEName = process.ac_step_e_name;
                    app.isDynamicStep = process.is_dynamic_step;
                    app.isAcknowledge = process.is_acknowledge;
                    result.Add(app);
                }
            }
            else
            {
                //簽核預覽(暫存單)時無process，所以撈sp
                string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
                if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

                string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType";
                result = this.NpgsqlSearchByList<ApplicationApproveProcess>(sql, new
                {
                    lang = MvcContext.UserInfo.logging_locale,
                    apply_number = apply_number,
                    applicationType = apply_type
                });

                if (result != null && result.Count > 0)
                {
                    //插入簽核流程前去重
                    result = result.Distinct().ToList();

                    foreach (ApplicationApproveProcess item in result)
                    {
                        if (!string.IsNullOrEmpty(item.signerQuitTime))
                        {
                            item.signerQuitTimeTrans = DateTime.Parse(item.signerQuitTime);
                        }
                    }
                }

            }

            return result;
        }

        public bool UpdateApplicationApprove(string apply_type, string form_type, string apply_number, int updateStepID, IDbContext? dbContext = null)
        {
            FlowStepRepository _stepRepository = new FlowStepRepository();
            //int stepID = _stepRepository.GetWhqLowProStepid(flow_id);

            //檢查是否已有完整簽核流程，如沒有則不可更改
            List<flow_step_process> oldStepList = GetFlowStepProcessList(apply_type, form_type, apply_number);
            if (oldStepList == null || oldStepList.Count == 0) return true;

            //申請單如沒有對應關卡則直接返回
            if (updateStepID == null) return true;

            //刪除原有簽核流程
            DeleteFlowStepProcessByStep(apply_number, updateStepID, dbContext);

            string spName = PublicHelper.GetApplicationApproveProcessSpName(apply_type, form_type);
            if (string.IsNullOrEmpty(spName)) throw new Exception(ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));

            string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType,  @flowStepID";
            List<ApplicationApproveProcess> flowList = this.NpgsqlSearchByList<ApplicationApproveProcess>(sql, new
            {
                lang = MvcContext.UserInfo.logging_locale,
                apply_number = apply_number,
                applicationType = apply_type,
                flowStepID = updateStepID
            });

            List<flow_step_process> ackList = new List<flow_step_process>();
            if (flowList != null && flowList.Count > 0)
            {
                //插入簽核流程前去重
                flowList = flowList.Distinct().ToList();

                foreach (var item in flowList)
                {
                    flow_step_process process = new flow_step_process();
                    process.apply_number = apply_number;
                    process.apply_type = apply_type;
                    process.form_type = form_type;
                    process.step_id = item.stepID;
                    process.signer_emplid = item.signerEmplid;
                    process.signer_deptid = item.signerDeptid;
                    process.dept_level = item.deptLevel;
                    process.signer_c_name = item.signerCName;
                    process.signer_e_name = item.signerEName;
                    if (!string.IsNullOrEmpty(item.signerQuitTime))
                    {
                        process.signer_quit_time = DateTime.Parse(item.signerQuitTime);
                    }
                    process.current_e_name = item.funEname;
                    process.current_c_name = item.funName;
                    process.step_name = item.stepName;
                    process.next_step_id = item.nextStepID;
                    process.step_type = Convert.ToInt16(item.stepType);
                    process.approval_type = Convert.ToInt16(item.approvalType);
                    process.approve_level = item.approveLevel;
                    process.ac_type_level = item.acTypeLevel;
                    process.ac_type = string.IsNullOrEmpty(item.acType) ? null : Convert.ToInt16(item.acType);
                    process.ac_type_e_name = item.acTypeEName;
                    process.ac_type_c_name = item.acTypeName;
                    process.ac_step = string.IsNullOrEmpty(item.acStep) ? null : Convert.ToInt16(item.acStep);
                    process.ac_step_level = item.acStepLevel;
                    process.ac_step_e_name = item.acStepEName;
                    process.ac_step_c_name = item.acStepName;
                    process.is_dynamic_step = item.isDynamicStep;
                    process.is_acknowledge = item.isAcknowledge;
                    process.create_user = MvcContext.UserInfo.current_emp;
                    process.create_time = DateTime.UtcNow;

                    ackList.Add(process);
                }

                InsertFlowStepProcess(ackList, dbContext);
            }

            return true;
        }

        #region 檢查簽核人員是否在職
        /// <summary>
        /// 檢查簽核人員是否在職
        /// </summary>
        /// <param name="signerEmplids"></param>
        /// <returns></returns>
        public List<string> GetSignerByOnJob(List<string> signerEmplids)
        {
            string getSignerByOnJobSql = @"
                   SELECT ee.emplid FROM (SELECT emplid FROM dbo.ps_sub_ee_lgl_vw_a WHERE ISNULL(termination,N'') = N'') AS ee
                   INNER JOIN (SELECT value AS emplid FROM STRING_SPLIT(@signerEmplids, ',')) AS se ON se.emplid = ee.emplid;";
            return this.NpgsqlSearchByList<string>(getSignerByOnJobSql, new { signerEmplids = string.Join(",", signerEmplids) });
        }
        #endregion
    }
}
