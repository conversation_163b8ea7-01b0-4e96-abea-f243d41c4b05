﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///未借出明細(擴展表)
    ///</summary>
    [SugarTable("paper_unlending_detail")]
    public partial class paper_unlending_detail
    {
           public paper_unlending_detail(){


           }
           /// <summary>
           /// Desc:借出明細id，主鍵自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int detail_id {get;set;}

           /// <summary>
           /// Desc:主表paper_lending_application.lend_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_lend_id {get;set;}

           /// <summary>
           /// Desc:紙本基本資料表paper_basic_data.basic_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_basic_id {get;set;}

           /// <summary>
           /// Desc:需求資訊表paper_lending_demand.demand_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_demand_id {get;set;}

           /// <summary>
           /// Desc:合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:合約名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_name {get;set;}

           /// <summary>
           /// Desc:紙本編號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_code {get;set;} = null!;

           /// <summary>
           /// Desc:紙本名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_name {get;set;} = null!;

           /// <summary>
           /// Desc:是否已經歸還，1：是；0：否
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? is_return {get;set;}

           /// <summary>
           /// Desc:是否已經被取件	空值：操作明細單之前的狀態	0：未取件(操作明細單之後)	1：取件
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? is_pickup_lend {get;set;}

           /// <summary>
           /// Desc:應歸還日期；	如有調閱申請單，則為實際調閱的結束日期	如果沒有則只會在取件的時候存在 取件日期 + paper_lending_demand.borrow_days	如果是為1天，則需要在今天12點之前歸還
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? should_return_time {get;set;}

           /// <summary>
           /// Desc:實際歸還日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? actual_return_time {get;set;}

           /// <summary>
           /// Desc:收件狀態
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? receive_status {get;set;}

           /// <summary>
           /// Desc:取件方式，參數表 sys_parameters.para_code = lib_pickupStatus
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pickup_status {get;set;}

           /// <summary>
           /// Desc:取件日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? pickup_time {get;set;}

           /// <summary>
           /// Desc:托運單號，當取件方式為托運時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? consignment_number {get;set;}

           /// <summary>
           /// Desc:取件者，員工工號，當取件方式為自取時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pickup_emplid {get;set;}

           /// <summary>
           /// Desc:實際取件時間，年月日時分，當取件方式為自取時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? actual_pickup_time {get;set;}

           /// <summary>
           /// Desc:逾期天數；歸還日期與應歸還日期的時間差
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? overdue_day {get;set;}

           /// <summary>
           /// Desc:實際借閱天數，已取件才會有值：取件日期開始計算
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? actual_borrow_days {get;set;}

           /// <summary>
           /// Desc:借出應歸還日，如借出申請單是從調閱申請單來的，則借出應歸還日應等於調閱申請單的實際開放期間迄日，因user會在1.0系統裡面變動，所以需要存儲，留存申請開始的日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? loan_due_date {get;set;}

           /// <summary>
           /// Desc:借出紙本歸還狀態，可多值，參數表 para_code = lib_returnStatus
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_return_status {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:入庫狀態，參數表 para_code = lib_paperEntryStatus
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_entry_status {get;set;}

           /// <summary>
           /// Desc:借出單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_number {get;set;}

    }
}
