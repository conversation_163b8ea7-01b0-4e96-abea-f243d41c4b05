﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///首頁定位佈局參照表
    ///</summary>
    [SugarTable("sys_home_position")]
    public partial class sys_home_position
    {
           public sys_home_position(){


           }
           /// <summary>
           /// Desc:員工工號，主鍵
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string emplid {get;set;} = null!;

           /// <summary>
           /// Desc:首頁定位json格式，以便後期動態添加值
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string home_position {get;set;} = null!;

           /// <summary>
           /// Desc:創建者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:全局佈局鎖，1：鎖定；0：解鎖
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool home_lock {get;set;}

    }
}
