﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///自動化排程對照表
    ///</summary>
    [SugarTable("sys_auto_job")]
    public partial class sys_auto_job
    {
           public sys_auto_job(){


           }
           /// <summary>
           /// Desc:序號，自增長，主鍵
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:排程名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string job_name {get;set;} = null!;

           /// <summary>
           /// Desc:排程路由
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string job_url {get;set;} = null!;

           /// <summary>
           /// Desc:排程執行後需要發送郵件
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string job_mail_address {get;set;} = null!;

           /// <summary>
           /// Desc:排程執行類型：參數表(jobRunType)	每天 1；	每週 2；	每月 3；
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int job_run_type {get;set;}

           /// <summary>
           /// Desc:每週執行：1 ~ 7；	每月執行：1 ~ 30/31
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? job_run_interval {get;set;}

           /// <summary>
           /// Desc:排程執行時間：00:00
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string job_run_time {get;set;} = null!;

           /// <summary>
           /// Desc:排程狀態：1：運行；0：停用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool job_status {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:項目描述
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string job_descr {get;set;} = null!;

           /// <summary>
           /// Desc:運行程式
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string job_run_program {get;set;} = null!;

           /// <summary>
           /// Desc:是否可以讓用戶進行維護：1：可以；0：不可以
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_modify {get;set;}

    }
}
