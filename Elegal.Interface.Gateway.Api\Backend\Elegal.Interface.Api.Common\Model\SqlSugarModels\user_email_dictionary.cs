﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///user -> 郵件模板開放欄位字典
    ///</summary>
    [SugarTable("user_email_dictionary")]
    public partial class user_email_dictionary
    {
           public user_email_dictionary(){


           }
           /// <summary>
           /// Desc:開放欄位編碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string field_code {get;set;} = null!;

           /// <summary>
           /// Desc:開放欄位中文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string field_cname {get;set;} = null!;

           /// <summary>
           /// Desc:開放欄位英文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string field_ename {get;set;} = null!;

           /// <summary>
           /// Desc:開放欄位來源
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? field_source {get;set;}

           /// <summary>
           /// Desc:開放欄位類型；sys_parameters.para_code = N'e_funModule'
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string field_type {get;set;} = null!;

           /// <summary>
           /// Desc:開放來源類型	P：存儲過程	V：視圖	F：方法
           /// Default:F
           /// Nullable:False
           /// </summary>           
           public string source_type {get;set;} = null!;

           /// <summary>
           /// Desc:開放來源需查詢參數
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? source_para {get;set;}

           /// <summary>
           /// Desc:是否需要超鏈接；0：否；1：是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool has_linkurl {get;set;}

           /// <summary>
           /// Desc:開放欄位對應的郵件類型，使用 ; 分割
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? mail_type {get;set;}

    }
}
