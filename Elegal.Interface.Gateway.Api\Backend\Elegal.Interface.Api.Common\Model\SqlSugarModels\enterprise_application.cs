﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///關企建檔主表
    ///</summary>
    [SugarTable("enterprise_application")]
    public partial class enterprise_application
    {
           public enterprise_application(){


           }
           /// <summary>
           /// Desc:申請單號，規則:AR+西元年+5碼流水號(次年重新計算)
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:關企建檔第二層級
           /// Default:AR
           /// Nullable:False
           /// </summary>           
           public string form_type {get;set;} = null!;

           /// <summary>
           /// Desc:填單人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? fill_emplid {get;set;}

           /// <summary>
           /// Desc:填單人部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? fill_deptid {get;set;}

           /// <summary>
           /// Desc:填單人siteid/location
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? fill_siteid {get;set;}

           /// <summary>
           /// Desc:填單人Location（具體辦公位置）
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? fill_desk_location {get;set;}

           /// <summary>
           /// Desc:承辦法務人員工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? legal_affairs_emplid {get;set;}

           /// <summary>
           /// Desc:承辦法務人員部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? legal_affairs_deptid {get;set;}

           /// <summary>
           /// Desc:經辦人，用戶輸入
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_emplid {get;set;}

           /// <summary>
           /// Desc:經辦人部門，用戶輸入
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_deptid {get;set;}

           /// <summary>
           /// Desc:經辦人電話，用戶輸入
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_phone {get;set;}

           /// <summary>
           /// Desc:經辦人郵箱，用戶輸入
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_email {get;set;}

           /// <summary>
           /// Desc:我方主體
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? entity_id {get;set;}

           /// <summary>
           /// Desc:他方(含簡稱)	輸入公司全名，以json格式存儲
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_party {get;set;}

           /// <summary>
           /// Desc:他方案件編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_party_number {get;set;}

           /// <summary>
           /// Desc:案件名稱(合約名稱)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_name {get;set;}

           /// <summary>
           /// Desc:案件內容摘要(合約背景摘要)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_bg_remark {get;set;}

           /// <summary>
           /// Desc:機密等級；sys_parameters.para_code = N'confidentStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? confiden_level {get;set;}

           /// <summary>
           /// Desc:REF主約(相關合約)編號/申請單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ref_number {get;set;}

           /// <summary>
           /// Desc:主約編號，默認為無主約(01)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? main_contract_number {get;set;}

           /// <summary>
           /// Desc:關聯合約群組代號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? group_contract_number {get;set;}

           /// <summary>
           /// Desc:合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:合約性質；sys_parameters.para_code = N'contractType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_type {get;set;}

           /// <summary>
           /// Desc:標的物；sys_parameters.para_code = N'targetType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_obj {get;set;}

           /// <summary>
           /// Desc:簽約日類型；sys_parameters.para_code = N'dateOptions'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sign_type {get;set;}

           /// <summary>
           /// Desc:簽約日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sign_date {get;set;}

           /// <summary>
           /// Desc:簽約日待確認原因；當無簽約日時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_sign_date {get;set;}

           /// <summary>
           /// Desc:生效日類型；sys_parameters.para_code = N'effDateOptions'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? eff_type {get;set;}

           /// <summary>
           /// Desc:生效日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? eff_date {get;set;}

           /// <summary>
           /// Desc:生效日待確認原因；當無生效日時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_eff_date {get;set;}

           /// <summary>
           /// Desc:到期日類型；sys_parameters.para_code = N'dateOptions'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? exp_type {get;set;}

           /// <summary>
           /// Desc:到期日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? exp_date {get;set;}

           /// <summary>
           /// Desc:到期日待確認原因；當無到期日時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_exp_date {get;set;}

           /// <summary>
           /// Desc:到期日是否延展；0：否；1：是
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? has_exp_extend {get;set;}

           /// <summary>
           /// Desc:其它說明/備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_remark {get;set;}

           /// <summary>
           /// Desc:是否有無金額；sys_parameters.para_code = N'amountStatus'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? is_having_money {get;set;}

           /// <summary>
           /// Desc:掛帳部門名稱，當存在金額時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? account_deptid {get;set;}

           /// <summary>
           /// Desc:申請日期，格式：2011-07-06 12:34:56
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_time {get;set;}

           /// <summary>
           /// Desc:已核准日期，合約管理作業關卡之前的結案日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? approved_date {get;set;}

           /// <summary>
           /// Desc:結案日期，合約管理作者者點擊end的日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? closed_date {get;set;}

           /// <summary>
           /// Desc:提前終止日期，只有在修改表單的時候會存在
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? early_cease_date {get;set;}

           /// <summary>
           /// Desc:案件狀態	T：暫存單(無必要參數，申請單號為經辦人工號)	E：已結案
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? application_state {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? create_user {get;set;}

           /// <summary>
           /// Desc:創建時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

           /// <summary>
           /// Desc:修改人員
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:是否為舊系統進入數據，1：是；0：否
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_old_system {get;set;}

           /// <summary>
           /// Desc:關企建檔類型；sys_parameters.para_code = N'enterpArchivingType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? enterp_archivie_type {get;set;}

           /// <summary>
           /// Desc:1.0承辦法務人員工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_legal_emplid {get;set;}

           /// <summary>
           /// Desc:1.0承辦法務人員部門代號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_legal_deptid {get;set;}

    }
}
