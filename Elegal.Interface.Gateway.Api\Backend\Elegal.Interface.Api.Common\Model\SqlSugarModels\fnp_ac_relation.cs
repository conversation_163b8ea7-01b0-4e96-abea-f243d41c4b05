﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///主體和實際公司對照表
    ///</summary>
    [SugarTable("fnp_ac_relation")]
    public partial class fnp_ac_relation
    {
           public fnp_ac_relation(){


           }
           /// <summary>
           /// Desc:主體ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:實際公司代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company {get;set;} = null!;

           /// <summary>
           /// Desc:創建人，操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
