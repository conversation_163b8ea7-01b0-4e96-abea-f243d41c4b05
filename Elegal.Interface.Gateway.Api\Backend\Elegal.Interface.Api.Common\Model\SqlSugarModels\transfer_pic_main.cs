﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///經辦人轉單主表
    ///</summary>
    [SugarTable("transfer_pic_main")]
    public partial class transfer_pic_main
    {
           public transfer_pic_main(){


           }
           /// <summary>
           /// Desc:序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public int transfer_id {get;set;}

           /// <summary>
           /// Desc:代碼(T)+西元年+5碼流水號(次年重新計算)；例如：T202400001
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string transfer_pic_number {get;set;} = null!;

           /// <summary>
           /// Desc:申請單狀態，sys_parameters.para_code = N'transApplyStatus'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string trans_application_status {get;set;} = null!;

           /// <summary>
           /// Desc:核決狀態，1：通過；2：駁回
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? approved_status {get;set;}

           /// <summary>
           /// Desc:簽核者工號	1、初始新增時，簽核者為對應申請單的pic_emplid的部門主管	2、當部門主管簽核通過時，簽核者為對應的交接人	3、當經辦人部門主管/交接人駁回時，統一修改為填單人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? approve_emplid {get;set;}

           /// <summary>
           /// Desc:填單人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string fill_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:申請單的經辦人/現任聯絡人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:交接人工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string handover_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:轉單備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? transfer_remarks {get;set;}

           /// <summary>
           /// Desc:申請日期
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime apply_time {get;set;}

           /// <summary>
           /// Desc:作廢原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? void_reason {get;set;}

           /// <summary>
           /// Desc:結案/作廢日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? complete_date {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人員
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:簽核者部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? approve_deptid {get;set;}

           /// <summary>
           /// Desc:新舊案件狀態；1：舊案件；0：新案件
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? is_new {get;set;}

           /// <summary>
           /// Desc:簽核原因，會被替換
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sign_options {get;set;}

           /// <summary>
           /// Desc:轉單部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? transfer_deptid {get;set;}

           /// <summary>
           /// Desc:填單人部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? fill_deptid {get;set;}

    }
}
