﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///會簽類型：金額判定條件
    ///</summary>
    [SugarTable("legal_assign_price")]
    public partial class legal_assign_price
    {
           public legal_assign_price(){


           }
           /// <summary>
           /// Desc:主體分類
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_type {get;set;} = null!;

           /// <summary>
           /// Desc:總經理判斷金額下限
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int rules_gmlowprice {get;set;}

           /// <summary>
           /// Desc:總經理判斷金額上限
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int rules_gmhighprice {get;set;}

           /// <summary>
           /// Desc:董事長判斷金額下限
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int rules_ceolowprice {get;set;}

           /// <summary>
           /// Desc:集團總經理部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? rules_corpgm_deptid {get;set;}

           /// <summary>
           /// Desc:集團總經理工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? rules_corpgm_emplid {get;set;}

           /// <summary>
           /// Desc:集團總經理名字
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? rules_corpgm_name {get;set;}

           /// <summary>
           /// Desc:集團董事長部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? rules_corpceo_deptid {get;set;}

           /// <summary>
           /// Desc:集團董事長工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? rules_corpceo_emplid {get;set;}

           /// <summary>
           /// Desc:集團董事長名字
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? rules_corpceo_name {get;set;}

    }
}
