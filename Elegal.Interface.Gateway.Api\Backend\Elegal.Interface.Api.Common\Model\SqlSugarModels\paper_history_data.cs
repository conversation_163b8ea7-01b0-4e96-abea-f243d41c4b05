﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///紙本歷程
    ///</summary>
    [SugarTable("paper_history_data")]
    public partial class paper_history_data
    {
           public paper_history_data(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int history_id {get;set;}

           /// <summary>
           /// Desc:紙本id，paper_basic_data.basic_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_basic_id {get;set;}

           /// <summary>
           /// Desc:入庫狀態，參數表 para_code = lib_paperEntryStatus
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_entry_status {get;set;} = null!;

           /// <summary>
           /// Desc:收件者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string course_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_remarks {get;set;}

           /// <summary>
           /// Desc:借出單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? borrow_applynumber {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
