﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///首頁區塊對照表
    ///</summary>
    [SugarTable("sys_home_block")]
    public partial class sys_home_block
    {
           public sys_home_block(){


           }
           /// <summary>
           /// Desc:序號；主鍵自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:編碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string code {get;set;} = null!;

           /// <summary>
           /// Desc:中文標題
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string c_title {get;set;} = null!;

           /// <summary>
           /// Desc:英文標題
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string e_title {get;set;} = null!;

           /// <summary>
           /// Desc:備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? remark {get;set;}

           /// <summary>
           /// Desc:是否可關閉，1：啟用；0：停用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool closeable {get;set;}

           /// <summary>
           /// Desc:排序
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? sort_order {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:更新者
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:更新時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:分組
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int group_index {get;set;}

    }
}
