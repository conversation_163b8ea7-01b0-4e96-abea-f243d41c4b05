﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///核決表對應的主體分類
    ///</summary>
    [SugarTable("legal_assign_entitytype_mapping")]
    public partial class legal_assign_entitytype_mapping
    {
           public legal_assign_entitytype_mapping(){


           }
           /// <summary>
           /// Desc:主體分類
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_type {get;set;} = null!;

           /// <summary>
           /// Desc:主體id對應fnp_entity
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:主體簡稱對應fnp_entity
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity {get;set;} = null!;

    }
}
