﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///承辦法務人員轉單歷程
    ///</summary>
    [SugarTable("transfer_legal_history")]
    public partial class transfer_legal_history
    {
           public transfer_legal_history(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與 申請單.apply_number 關聯
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:申請單的承辦法務人員工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_legal_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:交接人工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string handover_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:轉單備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? transfer_remarks {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:代碼(TL)+西元年+5碼流水號(次年重新計算)；例如：TL202400001
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? transfer_pic_number {get;set;}

    }
}
