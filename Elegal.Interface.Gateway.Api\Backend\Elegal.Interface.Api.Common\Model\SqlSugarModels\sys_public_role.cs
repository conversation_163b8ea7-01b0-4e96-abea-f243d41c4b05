﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///公版角色功能表
    ///</summary>
    [SugarTable("sys_public_role")]
    public partial class sys_public_role
    {
           public sys_public_role(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:公版角色名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string public_role_name {get;set;} = null!;

           /// <summary>
           /// Desc:是否使用；1：使用；0：禁用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int is_used {get;set;}

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
