﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///關企人員
    ///</summary>
    [SugarTable("affiliate_emp")]
    public partial class affiliate_emp
    {
           public affiliate_emp(){


           }
           /// <summary>
           /// Desc:人員工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string aff_empid {get;set;} = null!;

           /// <summary>
           /// Desc:員工編號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_empcode {get;set;} = null!;

           /// <summary>
           /// Desc:部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:中文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_emp_cname {get;set;} = null!;

           /// <summary>
           /// Desc:英文名
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? aff_emp_ename {get;set;}

           /// <summary>
           /// Desc:郵件地址
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_emp_email {get;set;} = null!;

           /// <summary>
           /// Desc:員工狀態；1：啟用；0：停用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_emp_status {get;set;} = null!;

           /// <summary>
           /// Desc:系統使用權限；1：可使用；0：不可使用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_emp_sysauth {get;set;} = null!;

           /// <summary>
           /// Desc:AD登入帳號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? aff_emp_ad {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
