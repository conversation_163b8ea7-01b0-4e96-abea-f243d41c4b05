﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///紙本基本資料(擴展表,已確認資料)
    ///</summary>
    [SugarTable("paper_basic_data")]
    public partial class paper_basic_data
    {
           public paper_basic_data(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int basic_id {get;set;}

           /// <summary>
           /// Desc:申請單主鍵，對應 paper_application_data.application_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_applica_id {get;set;}

           /// <summary>
           /// Desc:紙本編號，唯一，由申請單號自動排序(ex：C202300233-01)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_code {get;set;} = null!;

           /// <summary>
           /// Desc:紙本名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_name {get;set;} = null!;

           /// <summary>
           /// Desc:紙本類型，參數表 para_code = lib_paperType
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_type {get;set;} = null!;

           /// <summary>
           /// Desc:機密等級，參數表 para_code = confidentStatus
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_confiden_level {get;set;} = null!;

           /// <summary>
           /// Desc:借閱日上限，天數
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int parer_borrow_days {get;set;}

           /// <summary>
           /// Desc:存放位置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_position {get;set;}

           /// <summary>
           /// Desc:入庫狀態，參數表 para_code = lib_paperEntryStatus
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string paper_entry_status {get;set;} = null!;

           /// <summary>
           /// Desc:銷毀日期，當入庫狀態選擇已銷毀時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? destroy_time {get;set;}

           /// <summary>
           /// Desc:遺失日期，當入庫狀態選擇已遺失時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? lost_time {get;set;}

           /// <summary>
           /// Desc:紙本歸還現狀，可多值，參數表 para_code = lib_returnStatus，是在做歸還作業時候不同借出單的匯總
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_return_status {get;set;}

           /// <summary>
           /// Desc:是否為excel匯入 1：是；0：不是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int is_excel {get;set;}

           /// <summary>
           /// Desc:備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_remarks {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:銷毀原因，當入庫狀態選擇已銷毀時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? destroy_reason {get;set;}

           /// <summary>
           /// Desc:遺失原因，當入庫狀態選擇已遺失時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lost_reason {get;set;}

           /// <summary>
           /// Desc:申請單紙本資訊所需歸檔區文件清單
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? file_id {get;set;}

    }
}
