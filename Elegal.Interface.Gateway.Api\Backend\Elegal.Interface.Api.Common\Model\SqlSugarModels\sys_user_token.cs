﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///登陸者賬號token信息
    ///</summary>
    [SugarTable("sys_user_token")]
    public partial class sys_user_token
    {
           public sys_user_token(){


           }
           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string logging_user {get;set;} = null!;

           /// <summary>
           /// Desc:token
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string idToken {get;set;} = null!;

    }
}
