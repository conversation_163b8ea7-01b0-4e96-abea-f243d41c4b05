﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///2.0用戶異動記錄
    ///</summary>
    [SugarTable("sys_change_records")]
    public partial class sys_change_records
    {
           public sys_change_records(){


           }
           /// <summary>
           /// Desc:異動ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public long changeid {get;set;}

           /// <summary>
           /// Desc:異動日期
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:異動者公司ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company_id {get;set;} = null!;

           /// <summary>
           /// Desc:公司名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company_name {get;set;} = null!;

           /// <summary>
           /// Desc:部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:部門中文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string dept_descr {get;set;} = null!;

           /// <summary>
           /// Desc:異動者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string empid {get;set;} = null!;

           /// <summary>
           /// Desc:異動者姓名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;} = null!;

           /// <summary>
           /// Desc:原使用者公司ID，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_comp_id {get;set;}

           /// <summary>
           /// Desc:原使用者公司名稱，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_comp_descr {get;set;}

           /// <summary>
           /// Desc:原使用者部門代號，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_dept_id {get;set;}

           /// <summary>
           /// Desc:原使用者部門中文名稱，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_dept_descr {get;set;}

           /// <summary>
           /// Desc:原使用者工號，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_emp_id {get;set;}

           /// <summary>
           /// Desc:原使用者姓名，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_name {get;set;}

           /// <summary>
           /// Desc:申請單號/紙本編號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string work_key {get;set;} = null!;

           /// <summary>
           /// Desc:功能名稱，2.0對應menucode
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string work_item {get;set;} = null!;

           /// <summary>
           /// Desc:異動類型(一級)：A-建檔；C-合約申請；O-其他申請；P-紙本管理
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string change_type {get;set;} = null!;

           /// <summary>
           /// Desc:異動欄位code
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_code {get;set;}

           /// <summary>
           /// Desc:修改前內容
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? content_before {get;set;}

           /// <summary>
           /// Desc:檔案位置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_link {get;set;}

           /// <summary>
           /// Desc:合約編號，1.0未顯示在頁面上，待定
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:是否修改附件；1：是；0：否
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? is_modify_annex {get;set;}

           /// <summary>
           /// Desc:修改後的內容
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? content_after {get;set;}

           /// <summary>
           /// Desc:根據【申請單號/紙本編號】獲取最大一筆批次數據+1
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long batch_id {get;set;}

           /// <summary>
           /// Desc:異動欄位英文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_code_ename {get;set;}

           /// <summary>
           /// Desc:異動欄位中文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_code_cname {get;set;}

           /// <summary>
           /// Desc:異動內容來源查詢類型	1：參數表	2：人事資料表	3：主體表(關企+我方)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? change_code_type {get;set;}

           /// <summary>
           /// Desc:根據異動內容來源查詢類型存儲對應參數表para_code
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_code_source {get;set;}

           /// <summary>
           /// Desc:異動類型(二級)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_next_type {get;set;}

           /// <summary>
           /// Desc:異動欄位子code
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? children_code {get;set;}

           /// <summary>
           /// Desc:異動子欄位英文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? children_code_ename {get;set;}

           /// <summary>
           /// Desc:異動子欄位中名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? children_code_cname {get;set;}

    }
}
