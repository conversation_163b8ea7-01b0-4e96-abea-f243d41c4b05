﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///外派員工表
    ///</summary>
    [SugarTable("ps_exp_home_vw_a")]
    public partial class ps_exp_home_vw_a
    {
           public ps_exp_home_vw_a(){


           }
           /// <summary>
           /// Desc:員工工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string emplid {get;set;} = null!;

           /// <summary>
           /// Desc:中文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;} = null!;

           /// <summary>
           /// Desc:英文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name_a {get;set;} = null!;

           /// <summary>
           /// Desc:公司
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string company {get;set;} = null!;

           /// <summary>
           /// Desc:部門
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:工作所在地
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string site_id_a {get;set;} = null!;

           /// <summary>
           /// Desc:支薪地
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string sal_location_a {get;set;} = null!;

    }
}
