﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///紙本追蹤批次作業
    ///</summary>
    [SugarTable("paper_track_work")]
    public partial class paper_track_work
    {
           public paper_track_work(){


           }
           /// <summary>
           /// Desc:主鍵，序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int track_id {get;set;}

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:紙本追蹤狀態
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? track_status {get;set;}

           /// <summary>
           /// Desc:紙本收件方式=紙本取件方式	//01：讀卡感應；02：手動輸入	參數表 sys_parameters.para_code = lib_pickupStatus
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? receiving_type {get;set;}

           /// <summary>
           /// Desc:紙本收件者
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? receiving_emplid {get;set;}

           /// <summary>
           /// Desc:紙本收件日
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? receiving_date {get;set;}

           /// <summary>
           /// Desc:紙本份數
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? paper_number {get;set;}

           /// <summary>
           /// Desc:提醒次數(發一次郵件加1次)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? remind_count {get;set;}

           /// <summary>
           /// Desc:追蹤批次備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? track_remark {get;set;}

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:托運單號，當取件方式為托運時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? consignment_number {get;set;}

    }
}
