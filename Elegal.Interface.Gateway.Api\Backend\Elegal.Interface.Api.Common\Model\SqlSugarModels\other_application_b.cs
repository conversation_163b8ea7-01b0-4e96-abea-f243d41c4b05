﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///其他申請 -> B)：案件清單調閱
    ///</summary>
    [SugarTable("other_application_b")]
    public partial class other_application_b
    {
           public other_application_b(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public long rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與 other_application.apply_number 對應
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:清單查詢條件：主體(多筆，以 ; 區隔)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? detailed_entity_id {get;set;}

           /// <summary>
           /// Desc:清單查詢條件：其他，使用json格式存儲
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? detailed_other {get;set;}

           /// <summary>
           /// Desc:申請原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_reason {get;set;}

           /// <summary>
           /// Desc:申請開放期間(起日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_date_start {get;set;}

           /// <summary>
           /// Desc:申請開放期間(迄日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_date_end {get;set;}

           /// <summary>
           /// Desc:其他說明/備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_remark {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:清單查詢結果預估筆數
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? estimate_count {get;set;}

           /// <summary>
           /// Desc:清單查詢結果預估申請單號，與 estimate_count 結果一致
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? estimate_apply_number {get;set;}

           /// <summary>
           /// Desc:清單匯出所需欄位
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? export_field {get;set;}

           /// <summary>
           /// Desc:清單產生日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? estimate_datetime {get;set;}

           /// <summary>
           /// Desc:申請開放日期差值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? apply_open_time {get;set;}

           /// <summary>
           /// Desc:1.0清單查詢條件
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_condition {get;set;}

    }
}
