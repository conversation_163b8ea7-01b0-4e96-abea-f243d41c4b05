﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///關企主體(公司)
    ///</summary>
    [SugarTable("affiliate_company")]
    public partial class affiliate_company
    {
           public affiliate_company(){


           }
           /// <summary>
           /// Desc:公司代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string aff_company_code {get;set;} = null!;

           /// <summary>
           /// Desc:公司簡稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_company_abb {get;set;} = null!;

           /// <summary>
           /// Desc:中文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_company_cname {get;set;} = null!;

           /// <summary>
           /// Desc:英文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_company_ename {get;set;} = null!;

           /// <summary>
           /// Desc:hr公司代號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? aff_hrcompany_code {get;set;}

           /// <summary>
           /// Desc:狀態，1：啟用；0：禁用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_status {get;set;} = null!;

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:關聯我方公司主體，對應我方公司主體
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? aff_group_entity {get;set;}

           /// <summary>
           /// Desc:關企公司備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? aff_reamrk {get;set;}

    }
}
