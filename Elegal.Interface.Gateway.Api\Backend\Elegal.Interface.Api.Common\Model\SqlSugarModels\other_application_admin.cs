﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///其他申請 -> 行政資訊
    ///</summary>
    [SugarTable("other_application_admin")]
    public partial class other_application_admin
    {
           public other_application_admin(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與 other_application.apply_number 對應
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:法務最高主管，0：否；1：是
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? has_top_manager {get;set;}

           /// <summary>
           /// Desc:其他說明/備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? admin_reason {get;set;}

           /// <summary>
           /// Desc:實際開放期間(起日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? actual_date_start {get;set;}

           /// <summary>
           /// Desc:實際開放期間(迄日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? actual_date_end {get;set;}

           /// <summary>
           /// Desc:角色名稱，對應 p_role.r_id
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? auth_role_id {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:True
           /// </summary>           
           public DateTime? create_time {get;set;}

    }
}
