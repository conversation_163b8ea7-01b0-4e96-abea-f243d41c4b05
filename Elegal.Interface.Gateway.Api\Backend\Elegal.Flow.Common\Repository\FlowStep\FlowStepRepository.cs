﻿using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.DBModel.flow;
using Elegal.Interface.Api.Common.Model.DBModel.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel.McpResultModel;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Orm;
using System.Data;

namespace Elegal.Flow.Common.Repository.FlowStep
{
    public class FlowStepRepository : BaseRepository
    {
        #region 申請單簽核關卡匯總
        /// <summary>
        /// 申請單簽核關卡匯總
        /// </summary>
        private static readonly List<string> signOffStep = AppSettingHelper.GetValue("signOffStep").Split(",").ToList();
        #endregion

        #region 事務處理

        #region 獲取簽核人員
        /// <summary>
        /// 獲取簽核人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_signer> GetFlowStepSignerToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM flow_step_signer WHERE apply_number = @apply_number";
            return this.NpgsqlSearchByListToTransaction<flow_step_signer>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region 取得一般加簽人員
        /// <summary>
        /// 取得一般加簽人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_signer_invitee> GetFlowStepSignerInviteesToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_type = 1";
            return this.NpgsqlSearchByListToTransaction<flow_step_signer_invitee>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region 獲取特殊加簽人員
        /// <summary>
        /// 獲取特殊加簽人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_signer_invitee> GetFlowStepSignerInvitees_SpecialToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_type = 2";
            return this.NpgsqlSearchByListToTransaction<flow_step_signer_invitee>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region 獲取簽核步驟
        /// <summary>
        /// 獲取簽核步驟
        /// </summary>
        /// <param name="step_id"></param>
        /// <returns></returns>
        public flow_step GetFlowStepToTransaction(int step_id, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM flow_step WHERE step_id = @step_id";
            return this.NpgsqlSearchBySingleToTransaction<flow_step>(sql, dbContext, new { step_id });
        }
        #endregion

        #region 獲取簽核步驟
        /// <summary>
        /// 獲取簽核步驟
        /// </summary>
        /// <param name="current_step"></param>
        /// <param name="logging_locale"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public flow_step GetFlowStepByParaToTransaction(int current_step, string logging_locale, IDbContext dbContext)
        {
            string sql = $@"
SELECT fs.*,sp.fun_name AS memo FROM (SELECT step_id, flow_id, step_name, begin_time, step_type, approval_type, next_step_id, is_dynamic_step FROM flow_step WHERE step_id = @current_step) AS fs
INNER JOIN (
SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @logging_locale
AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)
) AS sp ON sp.func_code = CONVERT(NVARCHAR,fs.step_id);";
            return this.NpgsqlSearchBySingleToTransaction<flow_step>(sql, dbContext, new { current_step = current_step, logging_locale = logging_locale, signOffStep = string.Join(',', signOffStep.Select(s => s)) });
        }
        #endregion

        #region 法務最高主管StepID
        /// <summary>
        /// 法務最高主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetLegalTopSupervisorStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'LEGAL_TOP_SUPERVISOR'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 總部法務承辦關卡StepID
        /// <summary>
        /// 總部法務承辦關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetWhqLowProStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'WHQ_LOW_PRO'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 合約管理人關卡StepID
        /// <summary>
        /// 合約管理人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetContractMaTaskStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CONTRACT_MA_TASK'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 會簽關卡StepID
        /// <summary>
        /// 會簽關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetAcknowledgeStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'ACKNOWLEDGE'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region SiteGM關卡StepID
        /// <summary>
        /// SiteGM關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetSiteGMStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_SITGM'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 經辦人部門主管StepID
        /// <summary>
        /// 經辦人部門主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetAgnetSupervisorStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AGENT_SUPERVISOR'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 總經理關卡StepID
        /// <summary>
        /// 總經理關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetCeoStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_CEO'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 董事長關卡StepID
        /// <summary>
        /// 董事長關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetPresidentStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_PRESIDENT'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 被授權正本簽屬人關卡StepID
        /// <summary>
        /// 被授權正本簽屬人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetSignatoryStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AUTHORIZED_ORIGINAL_SIGNATORY'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 組織主管關卡StepID
        /// <summary>
        /// 組織主管關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetOrganizationStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'BU_BG_CORP_FUNC_MA'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 經辦人部門組織主管StepID
        /// <summary>
        /// 經辦人部門組織主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetAgentOrgSupervisorStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AGENT_ORG_SUPERVISOR'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 總部承辦財務部門主管StepID
        /// <summary>
        /// 總部承辦財務部門主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetWhqLowFinanceMaStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'WHQ_LOW_FINANCE_MA'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 調閱案件原始經辦單位最高主管
        /// <summary>
        /// 調閱案件原始經辦單位最高主管
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetRetrievePicTopSupervisorStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'RETRIEVE_PIC_TOP_SUPERVISOR'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 申請主體最高主管
        /// <summary>
        /// 申請主體最高主管
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetApplyEntityTopSupervisorStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'APPLY_ENTITY_TOP_SUPERVISOR'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 總部法務行政關卡StepID
        /// <summary>
        /// 總部法務行政關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetWhqLowAdminStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_WHQ_LOW_ADMIN'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 法定代表人StepID
        /// <summary>
        /// 法定代表人StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetCkLegalAgentStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_LEGAL_AGENT'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 總部承辦財務StepID
        /// <summary>
        /// 總部承辦財務StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetWhqFinanceProStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_WHQ_FINANCE_PRO'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 總部調閱行政關卡StepID
        /// <summary>
        /// 總部調閱行政關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetWhqRetrieveAdminStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'WHQ_RETRIEVE_ADMIN'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 特殊主體法務行政關卡StepID
        /// <summary>
        /// 特殊主體法務行政關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetSpecialWhqLowAdminStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'SPECIAL_WHQ_LOW_ADMIN'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 各Site合約管理人關卡StepID
        /// <summary>
        /// 各Site合約管理人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public int GetSiteContractMaTaskStepidToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'SITE_CONTRACT_MA_TASK'";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 根據申請單號獲取申請數據
        /// <summary>
        /// 根據申請單號獲取申請數據
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public V_GetAllApplication GetAllApplicationToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM V_GetAllApplication WHERE apply_number = @apply_number";
            return this.NpgsqlSearchBySingleToTransaction<V_GetAllApplication>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region 根據申請單類型+申請單號獲取申請單數據
        /// <summary>
        /// 根據申請單類型+申請單號獲取申請單數據
        /// </summary>
        /// <param name="apply_type"></param>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public V_GetAllApplication GetAllApplicationToTransaction(string apply_type, string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM V_GetAllApplication WHERE apply_type = @apply_type AND apply_number = @apply_number";
            return this.NpgsqlSearchBySingleToTransaction<V_GetAllApplication>(sql, dbContext, new { apply_type, apply_number });
        }
        #endregion

        #region 驗證申請單是否修改為【待歸檔】狀態
        /// <summary>
        /// 驗證申請單是否修改為【待歸檔】狀態
        /// </summary>
        /// <param name="applyNumber"></param>
        /// <param name="dbContext"></param>
        public void ApprovedFileUpdateExpirationDateToTransaction(string applyNumber, IDbContext dbContext)
        {
            //sit632 新增給水印失效文檔加過期時間
            string sql = @"--轉檔文件
                            update sys_upload_file 
                            set expiry_date = DATEADD(day, (select CONVERT(int,func_code) from sys_parameters where para_code = 'transferDay' and lang_type = 'ZH-TW'), GETUTCDATE())
                            where upload_key = @applyNumber and is_watermake = 1
                            --水印文件
                            update sys_upload_file 
                            set expiry_date = DATEADD(day, (select CONVERT(int,func_code) from sys_parameters where para_code = 'transferDay' and lang_type = 'ZH-TW'), GETUTCDATE())
                            where original_file_id in (select fileid from sys_upload_file where upload_key = @applyNumber) and upload_type = N'101'";
            this.ExecuteCommandToTransaction(sql, dbContext, new { applyNumber = applyNumber });
        }
        #endregion

        #region 獲取簽核按鈕
        /// <summary>
        /// 獲取簽核按鈕
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="step_id"></param>
        /// <param name="action_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public flow_step_action GetActionToTransaction(int flow_id, int step_id, int action_id, IDbContext dbContext)
        {
            string sql = $@"SELECT flow_id, step_id, action_id, action_name, next_step_id, is_send_mcp FROM flow_step_action WHERE flow_id = @flow_id AND step_id = @step_id AND action_id = @action_id";
            return this.NpgsqlSearchBySingleToTransaction<flow_step_action>(sql, dbContext, new { flow_id, step_id, action_id });
        }
        #endregion

        #region 根據申請單號刪除簽核人員
        /// <summary>
        /// 根據申請單號刪除簽核人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        public void DeleteFlowStepSignerToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql_delete = $@"DELETE FROM flow_step_signer WHERE apply_number = @apply_number";
            this.ExecuteCommandToTransaction(sql_delete, dbContext, new { apply_number = apply_number });
        }
        #endregion

        #region 插入待簽核人員
        /// <summary>
        /// 插入待簽核人員
        /// </summary>
        /// <param name="signer"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertFlowStepSignerToTransaction(flow_step_signer signer, IDbContext dbContext)
        {
            //Issue：238 -> 添加會簽層級插入
            string sql = $@"
                                INSERT INTO flow_step_signer (flow_id, step_id, apply_number, signer_time, signer_emplid, signer_deptid, is_return, return_step_id, is_invitee, is_acknowledge, is_error, is_reject, create_time, create_user, acknowledge_step)
                                VALUES (@flow_id, @step_id, @apply_number, @signer_time, @signer_emplid, @signer_deptid, @is_return, @return_step_id, @is_invitee, @is_acknowledge, @is_error, @is_reject, @create_time, @create_user,@acknowledge_step)";
            return this.ExecuteCommand(sql, signer) > 0;
        }
        #endregion

        #region 刪除簽核人員
        /// <summary>
        /// 刪除簽核人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="emplid"></param>
        /// <param name="dbContext"></param>
        public void DeleteFlowStepSignerByEmplidToTransaction(string apply_number, string emplid, IDbContext dbContext)
        {
            string sql_agent = $@"DELETE fs
                                    FROM flow_step_signer fs
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fs.signer_emplid
                                    AND sa.auth_deptid = fs.signer_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fs.apply_number = @apply_number";
            this.ExecuteCommandToTransaction(sql_agent, dbContext, new { apply_number, signer_emplid = emplid });

            string sql_delete = $@"	DELETE FROM flow_step_signer 
                                        WHERE apply_number = @apply_number 
                                        AND signer_emplid = @signer_emplid";
            this.ExecuteCommandToTransaction(sql_delete, dbContext, new { apply_number, signer_emplid = emplid });
        }
        #endregion

        #region 獲取結案按鈕
        /// <summary>
        /// 獲取結案按鈕
        /// </summary>
        /// <param name="flow_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public flow_step_action GetEndActionToTransaction(int flow_id, IDbContext dbContext)
        {
            string sql = $@"SELECT flow_id, step_id, action_id, action_name, next_step_id, is_send_mcp FROM flow_step_action WHERE flow_id = @flow_id AND action_id = 99";
            return this.NpgsqlSearchBySingleToTransaction<flow_step_action>(sql, dbContext, new { flow_id });
        }
        #endregion

        #region 插入簽核歷程
        /// <summary>
        /// 插入簽核歷程
        /// </summary>
        /// <param name="history"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertFlowStepHistoryToTransaction(flow_step_history history, IDbContext dbContext)
        {
            //Issue：238 -> 添加會簽層級插入
            string sql = $@"
                                INSERT INTO flow_step_history (flow_id, apply_number, step_id, apply_sequence, step_action, step_opinion, actual_signer_emplid, actual_signer_name, actual_signer_name_a, actual_signer_deptid, should_signer_emplid, should_signer_name, should_signer_name_a, should_signer_deptid, create_time, acknowledge_step)
                                VALUES (@flow_id, @apply_number, @step_id, @apply_sequence,@step_action, @step_opinion, @actual_signer_emplid, @actual_signer_name, @actual_signer_name_a, @actual_signer_deptid, @should_signer_emplid, @should_signer_name, @should_signer_name_a, @should_signer_deptid, @create_time, @acknowledge_step)";
            return this.ExecuteCommandToTransaction(sql, dbContext, history) > 0;
        }
        #endregion

        #region 新增合約管理作業歷程
        /// <summary>
        /// 新增合約管理作業歷程
        /// </summary>
        /// <param name="history"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertContractHistoryToTransaction(flow_contract_history history, IDbContext dbContext)
        {
            string sql = $@"INSERT INTO flow_contract_history (flow_id, apply_number, step_id, apply_sequence, step_action, step_opinion, actual_signer_emplid, actual_signer_name, actual_signer_name_a, actual_signer_deptid, should_signer_emplid, should_signer_name, should_signer_name_a, should_signer_deptid, create_time, contract_step, contract_options)
                                VALUES (@flow_id, @apply_number, @step_id, @apply_sequence,@step_action, @step_opinion, @actual_signer_emplid, @actual_signer_name, @actual_signer_name_a, @actual_signer_deptid, @should_signer_emplid, @should_signer_name, @should_signer_name_a, @should_signer_deptid, @create_time, @contract_step, @contract_options)";
            return this.ExecuteCommandToTransaction(sql, dbContext, history) > 0;
        }
        #endregion

        #region 刪除會簽人員
        /// <summary>
        /// 刪除會簽人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        public void DeleteFlowStepSignerAcknowledgeToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql_delete = $@"DELETE FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number";
            this.ExecuteCommandToTransaction(sql_delete, dbContext, new { apply_number = apply_number });
        }
        #endregion

        #region 根據stepid刪除會簽人員
        /// <summary>
        /// 根據stepid刪除會簽人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="step_id"></param>
        /// <param name="dbContext"></param>
        public void DeleteFlowStepSignerAcknowledgeToTransaction(string apply_number, int step_id, IDbContext dbContext)
        {
            string sql_delete = $@"DELETE FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number AND step_id =  @step_id";
            this.ExecuteCommandToTransaction(sql_delete, dbContext, new { apply_number = apply_number, step_id = step_id });
        }
        #endregion

        #region 刪除組織主管
        /// <summary>
        /// 刪除組織主管
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool DeleteFlowStepSignerOrgToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"DELETE FROM flow_step_signer_org WHERE apply_number = @apply_number";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number }) > 0;
        }
        #endregion

        #region 刪除加簽人員
        /// <summary>
        /// 刪除加簽人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool DeleteFlowStepSignerInviteeToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql_invitee = $@"DELETE FROM flow_step_signer_invitee WHERE apply_number = @apply_number";
            return this.ExecuteCommandToTransaction(sql_invitee, dbContext, new { apply_number }) > 0;
        }
        #endregion

        #region 新增會簽人員
        /// <summary>
        /// 新增會簽人員
        /// </summary>
        /// <param name="acknowledge"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertFlowStepSignerAcknowledgeToTransaction(flow_step_signer_acknowledge acknowledge, IDbContext dbContext)
        {
            //Issue：443 -> 添加會簽部門的層級存儲
            string sql = $@"
                                INSERT INTO flow_step_signer_acknowledge (apply_number, acknow_emplid, acknow_deptid, acknow_level, step_id, acknow_reason, is_used,acknow_deptid_level)
                                VALUES (@apply_number, @acknow_emplid, @acknow_deptid, @acknow_level, @step_id, @acknow_reason, @is_used,@acknow_deptid_level)";
            return this.ExecuteCommandToTransaction(sql, dbContext, acknowledge) > 0;
        }
        #endregion

        #region 根據主體+合約性質獲取核決權限表
        /// <summary>
        /// 根據主體+合約性質獲取核決權限表
        /// </summary>
        /// <param name="entity_id"></param>
        /// <param name="contract_type"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<V_GetLegalAssign> GetLegalAssignListToTransaction(string entity_id, string contract_type, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM V_GetLegalAssign WHERE entity_id = @entity_id AND contract_type = @contract_type";
            return this.NpgsqlSearchByListToTransaction<V_GetLegalAssign>(sql, dbContext, new { entity_id, contract_type });
        }
        #endregion

        #region 獲取會簽人員數據
        /// <summary>
        /// 獲取會簽人員數據
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_signer_acknowledge> GetFlowStepSignerAcknowledgeToTransaction(string apply_number, IDbContext dbContext)
        {
            //Issue：443 -> 根據會簽部門層級排序
            string sql = $@"SELECT * FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number ORDER BY acknow_deptid_level DESC";
            return this.NpgsqlSearchByListToTransaction<flow_step_signer_acknowledge>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region 獲取組織主管數據
        /// <summary>
        /// 獲取組織主管數據
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_signer_org> GetFlowStepSignerOrgToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM flow_step_signer_org WHERE apply_number = @apply_number ORDER BY org_level DESC";
            return this.NpgsqlSearchByListToTransaction<flow_step_signer_org>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region reject或withdraw後的簽核紀錄
        /// <summary>
        /// reject或withdraw後的簽核紀錄
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_history> GetCurrentFlowStepHistoryToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"WITH LastReject AS (
                                SELECT MAX(create_time) AS last_reject_time
                                FROM flow_step_history
                                WHERE apply_number = @apply_number
                                AND step_action IN (3, 6)
                                )
                                SELECT *
                                FROM flow_step_history
                                WHERE apply_number = @apply_number
                                AND (create_time > (SELECT last_reject_time FROM LastReject) OR (SELECT last_reject_time FROM LastReject) IS NULL)
                                ORDER BY create_time ASC;";
            return this.NpgsqlSearchByListToTransaction<flow_step_history>(sql, dbContext, new { apply_number });
        }
        #endregion

        #region 根據主體獲取核決權限表
        /// <summary>
        /// 根據主體獲取核決權限表
        /// </summary>
        /// <param name="entity_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public V_GetLegalAssign GetLegalAssignByEntityToTransaction(string entity_id, IDbContext dbContext)
        {
            string sql = $@"SELECT TOP(1) * FROM V_GetLegalAssign WHERE entity_id = @entity_id";
            return this.NpgsqlSearchBySingleToTransaction<V_GetLegalAssign>(sql, dbContext, new { entity_id });
        }
        #endregion

        #region 修改簽核人員數據，改為已經使用
        /// <summary>
        /// 修改簽核人員數據，改為已經使用
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="signer_emplid"></param>
        /// <param name="signer_deptid"></param>
        /// <param name="acknow_level"></param>
        /// <returns></returns>
        public bool SetAcknowledgeSignerUsedByEmplidToTransaction(IDbContext dbContext, string apply_number, string signer_emplid, string signer_deptid, int? acknow_level = null)
        {
            string sql = $@"UPDATE dbo.flow_step_signer_acknowledge SET is_used = 1
                                     WHERE acknow_emplid = @signer_emplid
                                     AND acknow_deptid = @signer_deptid
                                     AND apply_number = @apply_number";
            //Issue：238 會簽滑關時，需要根據層級進行人員的by pass
            if (acknow_level != null)
            {
                sql += $@" AND acknow_level = @acknow_level";
            }

            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number, signer_emplid, signer_deptid, acknow_level }) > 0;
        }
        #endregion

        #region 刪除簽核人員數據
        /// <summary>
        /// 刪除簽核人員數據
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        public void DeleteFlowStepSignerByEmplidToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql_agent = $@"DELETE fs
                                    FROM flow_step_signer fs
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fs.signer_emplid
                                    AND sa.auth_deptid = fs.signer_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fs.apply_number = @apply_number";
            this.ExecuteCommandToTransaction(sql_agent, dbContext, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp });

            string sql_delete = $@"	DELETE FROM flow_step_signer 
                                        WHERE apply_number = @apply_number 
                                        AND signer_emplid = @signer_emplid";
            this.ExecuteCommandToTransaction(sql_delete, dbContext, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp });
        }
        #endregion

        #region 正本簽署人簽核數據修改
        /// <summary>
        /// 正本簽署人簽核數據修改
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="emplid"></param>
        /// <param name="deptid"></param>
        /// <returns></returns>
        public bool SetAcknowledgeSignerUsedByEmplidAndAgentToTransaction(IDbContext dbContext, string apply_number, string emplid, string deptid, int? minAckLevel = null)
        {
            //更新代理人
            string sql_agent = $@"UPDATE fsa
								    SET fsa.is_used = 1
                                  FROM flow_step_signer_acknowledge fsa
                                  INNER JOIN flow_step_signer fs ON fs.apply_number = fsa.apply_number
                                    AND fs.signer_emplid = fsa.acknow_emplid
                                    AND fs.signer_deptid = fsa.acknow_deptid
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fsa.acknow_emplid
                                    AND sa.auth_deptid = fsa.acknow_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fsa.apply_number = @apply_number {(minAckLevel != null ? (@" AND acknow_level = @minAckLevel") : "")}";
            this.ExecuteCommandToTransaction(sql_agent, dbContext, new { apply_number, signer_emplid = emplid, signer_deptid = deptid, minAckLevel = minAckLevel });

            //更新簽核人
            string sql = $@"UPDATE flow_step_signer_acknowledge
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_acknowledge.apply_number = fs.apply_number
                                AND flow_step_signer_acknowledge.acknow_emplid = fs.signer_emplid
                                AND flow_step_signer_acknowledge.acknow_deptid = fs.signer_deptid
                                AND fs.signer_emplid = @signer_emplid
                                AND fs.apply_number = @apply_number
                                 {(minAckLevel != null ? (@" AND acknow_level = @minAckLevel") : "")}";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number, signer_emplid = emplid, signer_deptid = deptid, minAckLevel = minAckLevel }) > 0;
        }
        #endregion

        #region 新增組織主管簽核人員
        /// <summary>
        /// 新增組織主管簽核人員
        /// </summary>
        /// <param name="signerOrg"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertFlowStepSignerOrgToTransaction(flow_step_signer_org signerOrg, IDbContext dbContext)
        {
            string sql = $@"INSERT INTO flow_step_signer_org (apply_number, org_emplid, org_deptid, org_level, step_id, org_type, is_used)
                                VALUES (@apply_number, @org_emplid, @org_deptid, @org_level, @step_id, @org_type, @is_used)";
            return this.ExecuteCommandToTransaction(sql, dbContext, signerOrg) > 0;
        }
        #endregion

        #region 將組織主管簽核人員設置為已使用
        /// <summary>
        /// 將組織主管簽核人員設置為已使用
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="signer_emplid"></param>
        /// <param name="signer_deptid"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool SetOrganizationSignerUsedByEmplidToTransaction(string apply_number, string signer_emplid, string signer_deptid, IDbContext dbContext)
        {
            string sql = $@"UPDATE flow_step_signer_org
								    SET is_used = 1
                                  WHERE org_emplid = @signer_emplid
                                    AND org_deptid = @signer_deptid
                                    AND apply_number = @apply_number";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number, signer_emplid, signer_deptid }) > 0;
        }
        #endregion

        #region 獲取同一關卡簽核序號
        /// <summary>
        /// 獲取同一關卡簽核序號
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="step_id"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public List<flow_step_history> GetFlowHistoryToTransaction(string apply_number, int step_id, IDbContext dbContext)
        {
            string sql = $@"SELECT * FROM flow_step_history WHERE apply_number = @apply_number AND step_id = @step_id ORDER BY rowid ASC";
            return this.NpgsqlSearchByListToTransaction<flow_step_history>(sql, dbContext, new { apply_number, step_id });
        }
        #endregion

        #region 根據申請單號驗證是否存在加簽人員數據
        /// <summary>
        /// 根據申請單號驗證是否存在加簽人員數據  Issue：200
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool CheckHasFlowStepSignerInviteesToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"SELECT COUNT(apply_number) FROM flow_step_signer_invitee WHERE apply_number = @apply_number;";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValueToTransaction(sql.ToString(), dbContext, new { apply_number = apply_number })) > 0;
        }
        #endregion

        #region 根據申請單號將組織主管簽核設置為已使用
        /// <summary>
        /// 根據申請單號將組織主管簽核設置為已使用
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool SetOrganizationSignerUsedToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"UPDATE flow_step_signer_org
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_org.apply_number = fs.apply_number
                                AND flow_step_signer_org.org_emplid = fs.signer_emplid
                                AND flow_step_signer_org.org_deptid = fs.signer_deptid
                                AND fs.apply_number = @apply_number;";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number }) > 0;
        }
        #endregion

        #region 將會簽人員數據改為已經使用
        /// <summary>
        /// 將會簽人員數據改為已經使用
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool SetAcknowledgeSignerUsedToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"UPDATE flow_step_signer_acknowledge
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_acknowledge.apply_number = fs.apply_number
                                AND flow_step_signer_acknowledge.acknow_emplid = fs.signer_emplid
                                AND flow_step_signer_acknowledge.acknow_deptid = fs.signer_deptid
                                AND fs.apply_number = @apply_number;";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number }) > 0;
        }
        #endregion

        #region 根據申請單號修改會簽數據
        /// <summary>
        /// 根據申請單號修改會簽數據
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public bool SetAcknowledgeSignerUsedByEmplidToTransaction(IDbContext dbContext, string apply_number, int? minAckLevel = null)
        {
            //更新代理人
            string sql_agent = $@"UPDATE fsa
								    SET fsa.is_used = 1
                                  FROM flow_step_signer_acknowledge fsa
                                  INNER JOIN flow_step_signer fs ON fs.apply_number = fsa.apply_number
                                    AND fs.signer_emplid = fsa.acknow_emplid
                                    AND fs.signer_deptid = fsa.acknow_deptid
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fsa.acknow_emplid
                                    AND sa.auth_deptid = fsa.acknow_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fsa.apply_number = @apply_number {(minAckLevel != null ? (@" AND acknow_level = @minAckLevel") : "")}";
            this.ExecuteCommandToTransaction(sql_agent, dbContext, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp, signer_deptid = MvcContext.UserInfo.current_dept, minAckLevel = minAckLevel });

            //更新簽核人
            string sql = $@"UPDATE flow_step_signer_acknowledge
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_acknowledge.apply_number = fs.apply_number
                                AND flow_step_signer_acknowledge.acknow_emplid = fs.signer_emplid
                                AND flow_step_signer_acknowledge.acknow_deptid = fs.signer_deptid
                                AND fs.signer_emplid = @signer_emplid
                                AND fs.apply_number = @apply_number
                                 {(minAckLevel != null ? (@" AND acknow_level = @minAckLevel") : "")}";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp, signer_deptid = MvcContext.UserInfo.current_dept, minAckLevel = minAckLevel }) > 0;
        }
        #endregion

        #region Reject、Withdraw或Return時：清空後送判斷原因和最終簽署人簽核關卡的選項值
        /// <summary>
        /// Issue：92 Reject、Withdraw或Return時：清空後送判斷原因和最終簽署人簽核關卡的選項值
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool UpdateByPassReasonAndFinalSignLevelToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql = $@"UPDATE dbo.form_application_admin SET final_sign_level = NULL,by_pass_reason = NULL WHERE apply_number = @apply_number;";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number = apply_number }) > 0;
        }
        #endregion

        #region 刪除未做過會簽補簽的人員數據
        /// <summary>
        /// 刪除未做過會簽補簽的人員數據  Issue：200
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="dbContext"></param>
        public void DeleteFlowStepSignerAcknowledgeByNoUsedToTransaction(string apply_number, IDbContext dbContext)
        {
            string sql_delete = $@"DELETE FROM dbo.flow_step_signer_acknowledge WHERE is_used = 0 AND apply_number = @apply_number;";
            this.ExecuteCommandToTransaction(sql_delete, dbContext, new { apply_number = apply_number });
        }
        #endregion

        #region 新增加簽人員數據
        /// <summary>
        /// 新增加簽人員數據
        /// </summary>
        /// <param name="invitee"></param>
        /// <param name="dbContext"></param>
        /// <returns></returns>
        public bool InsertFlowStepSignerInviteeToTransaction(flow_step_signer_invitee invitee, IDbContext dbContext)
        {
            string sql = $@"INSERT INTO flow_step_signer_invitee (flow_id, step_id, apply_number, invitee_emplid, invitee_deptid, invitee_remark, is_error, create_user, create_time, invitee_type, is_acknowledge)
                                VALUES (@flow_id, @step_id, @apply_number, @invitee_emplid, @invitee_deptid, @invitee_remark, @is_error, @create_user, @create_time, @invitee_type, @is_acknowledge)";
            return this.ExecuteCommandToTransaction(sql, dbContext, invitee) > 0;
        }
        #endregion

        #region 修改主表加簽人數據
        /// <summary>
        /// 修改主表加簽人數據
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_type"></param>
        /// <param name="apply_number"></param>
        /// <param name="signatory"></param>
        /// <returns></returns>
        public bool UpdateFormApply_SignatoryToTransaction(IDbContext dbContext, string apply_type, string apply_number, string? signatory)
        {
            string sql = string.Empty;
            if (apply_type == "C")
                sql = $@"UPDATE form_application
                            SET signatory = @signatory
                            WHERE apply_number = @apply_number";
            else if (apply_type == "O")
                sql = $@"UPDATE other_application
                            SET signatory = @signatory
                            WHERE apply_number = @apply_number";
            else return false;

            return this.ExecuteCommandToTransaction(sql, dbContext, new { signatory, apply_number }) > 0;
        }
        #endregion

        #region 將主簽核人賦值為加簽中
        /// <summary>
        /// 將主簽核人賦值為加簽中
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="is_invitee"></param>
        /// <returns></returns>
        public bool UpdateFlowStepSigner_IsInviteeToTransaction(IDbContext dbContext, string apply_number, int is_invitee)
        {
            string sql = $@"UPDATE flow_step_signer
                            SET is_invitee = @is_invitee
                            WHERE apply_number = @apply_number";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { is_invitee, apply_number }) > 0;
        }
        #endregion

        #region 檢查當前加簽人員是否是會簽補簽人員
        /// <summary>
        /// 檢查當前加簽人員是否是會簽補簽人員
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="emplid"></param>
        /// <returns></returns>
        public bool CheckIsAcknowledgeInviteeToTransaction(IDbContext dbContext, string apply_number, string emplid)
        {
            string sql = $@"SELECT COUNT(1) FROM dbo.flow_step_signer_invitee WHERE is_acknowledge = 1 AND apply_number = @apply_number AND invitee_emplid = @emplid;";
            return this.NpgsqlSearchBySingleToTransaction<int>(sql, dbContext, new { apply_number, emplid }) > 0;
        }
        #endregion

        #region 設置會簽人員為使用
        /// <summary>
        /// 設置會簽人員為使用
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="signer_emplid"></param>
        /// <returns></returns>
        public bool SetAcknowledgeSignerUsedByEmplidToTransaction(IDbContext dbContext, string apply_number, string signer_emplid)
        {
            string sql = $@"UPDATE flow_step_signer_acknowledge
								    SET is_used = 1
                                  WHERE acknow_emplid = @signer_emplid
                                    AND apply_number = @apply_number";
            return this.ExecuteCommandToTransaction(sql, dbContext, new { apply_number, signer_emplid }) > 0;
        }
        #endregion

        #region 刪除加簽人員
        /// <summary>
        /// 刪除加簽人員
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="emplid"></param>
        /// <returns></returns>
        public bool SetInviteeSignerUsedToTransaction(IDbContext dbContext, string apply_number, string emplid)
        {
            string sql_invitee = $@"DELETE FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_emplid = @emplid";
            return this.ExecuteCommandToTransaction(sql_invitee, dbContext, new { apply_number, emplid }) > 0;
        }
        #endregion

        #region 刪除特殊加簽人員
        /// <summary>
        /// 刪除特殊加簽人員
        /// </summary>
        /// <param name="dbContext"></param>
        /// <param name="apply_number"></param>
        /// <param name="sp_invite_level"></param>
        /// <returns></returns>
        public bool DeleteFlowStepSignerInviteeSpecialToTransaction(IDbContext dbContext, string apply_number, int sp_invite_level)
        {
            string sql_invitee = $@"DELETE FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_type = 2 AND sp_invite_level = @sp_invite_level";
            return this.ExecuteCommand(sql_invitee, new { apply_number, sp_invite_level }) > 0;
        }
        #endregion

        #endregion

        public flow_step GetFlowStep(int step_id)
        {
            string sql = $@"SELECT * FROM flow_step WHERE step_id = @step_id";
            return this.NpgsqlSearchBySingle<flow_step>(sql, new { step_id });
        }

        public flow_step GetFlowStepByPara(int current_step, string logging_locale)
        {
            string sql = $@"
SELECT fs.*,sp.fun_name AS memo FROM (SELECT step_id, flow_id, step_name, begin_time, step_type, approval_type, next_step_id, is_dynamic_step FROM flow_step WHERE step_id = @current_step) AS fs
INNER JOIN (
SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = @logging_locale
AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)
) AS sp ON sp.func_code = CONVERT(NVARCHAR,fs.step_id);";
            return this.NpgsqlSearchBySingle<flow_step>(sql, new { current_step = current_step, logging_locale = logging_locale, signOffStep = string.Join(',', signOffStep.Select(s => s)) });
        }

        #region 獲取會簽簽核層級關卡
        /// <summary>
        /// 獲取會簽簽核層級關卡
        /// Issue：238
        /// </summary>
        /// <param name="step_id"></param>
        /// <param name="logging_locale"></param>
        /// <returns></returns>
        public flow_step GetAcknowledgeStep(int step_id, string logging_locale)
        {
            string sql = $@"SELECT fun_name AS memo FROM dbo.sys_parameters WHERE para_code = N'acknowledgeStep' AND lang_type = @logging_locale AND func_code = CONVERT(NVARCHAR,@step_id);";
            return this.NpgsqlSearchBySingle<flow_step>(sql, new { step_id = step_id, logging_locale = logging_locale });
        }
        #endregion

        public List<FlowStepSignerResultModel> GetSysApproverManagementSigner(int step_id, string approver_type, string entity_id)
        {
            string sql = $@" SELECT @step_id AS step_id, sam.emp_id AS signer_emplid, ee.deptid AS signer_deptid FROM sys_approver_management sam
                             LEFT JOIN ps_sub_ee_lgl_vw_a ee ON sam.emp_id = ee.emplid
                             WHERE approver_type = @approver_type and entity_id = @entity_id";
            return this.NpgsqlSearchByList<FlowStepSignerResultModel>(sql, new { step_id, approver_type, entity_id });
        }

        public List<FlowStepSignerResultModel> GetSysApproverManagementSignerFinance(int step_id, string approver_type, string entity_id, string contract_fnid, string pay_style)
        {
            string sql = $@" SELECT @step_id AS step_id, sam.emp_id AS signer_emplid, ee.deptid AS signer_deptid FROM sys_approver_management sam
                             LEFT JOIN ps_sub_ee_lgl_vw_a ee ON sam.emp_id = ee.emplid
                             WHERE approver_type = @approver_type AND entity_id = @entity_id AND contract_fnid = @contract_fnid AND pay_style = @pay_style";
            return this.NpgsqlSearchByList<FlowStepSignerResultModel>(sql, new { step_id, approver_type, entity_id, contract_fnid, pay_style });
        }

        public string GetDeptManager(string deptid)
        {
            string sql = $@"select manager_id from ps_sub_og_lgl_vw_a WHERE deptid = @deptid";
            return this.NpgsqlSearchBySingle<string>(sql, new { deptid });
        }

        /// <summary>
        /// 根据单号获取单据类型
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public ApplyNumberTypeDto GetFormTypeByApplyNumber(string apply_number)
        {
            string sql = $@"select 
CASE 
        WHEN a.para_code = N'formType' THEN N'合約申請單'
        WHEN a.para_code = N'otherApplyType' THEN N'其他申請單'
        WHEN a.para_code = N'applicationType' THEN N'建檔申請單'
        ELSE a.para_code
END AS fun_type 
,fun_name from sys_parameters a,V_GetAllApplication b 
                            where a.para_code in (N'formType',N'otherApplyType',N'applicationType') 
                            and b.apply_number = @apply_number
                            and b.form_type = a.func_code
                            and lang_type=@lang_type";
            return NpgsqlSearchBySingle<ApplyNumberTypeDto>(sql, new { apply_number, lang_type = MvcContext.UserInfo.logging_locale });
        }

        public flow_step_action GetAction(int flow_id, int step_id, int action_id)
        {
            string sql = $@"SELECT flow_id, step_id, action_id, action_name, next_step_id, is_send_mcp FROM flow_step_action WHERE flow_id = @flow_id AND step_id = @step_id AND action_id = @action_id";
            return this.NpgsqlSearchBySingle<flow_step_action>(sql, new { flow_id, step_id, action_id });
        }

        public int GetReturnActionId(int step_id)
        {
            string sql = $@"SELECT action_id FROM flow_step_action WHERE action_name like 'return%' AND step_id = @step_id";
            return this.NpgsqlSearchBySingle<int>(sql, new { step_id });
        }

        public string GetActionName(int step_id, int action_id, string actionName)
        {
            string sql = $@"SELECT display_name FROM flow_step_action WHERE step_id = @step_id AND action_id = @action_id";
            return this.NpgsqlSearchBySingle<string>(sql, new { step_id, action_id }) ?? actionName;
        }

        public flow_step_action GetEndAction(int flow_id)
        {
            string sql = $@"SELECT flow_id, step_id, action_id, action_name, next_step_id, is_send_mcp FROM flow_step_action WHERE flow_id = @flow_id AND action_id = 99";
            return this.NpgsqlSearchBySingle<flow_step_action>(sql, new { flow_id });
        }

        public flow_step_action GetReturnAction(int flow_id, int step_id, int action_id)
        {
            string sql = $@"SELECT flow_id, step_id, action_id, action_name, next_step_id, is_send_mcp FROM flow_step_action WHERE flow_id = @flow_id AND step_id = @step_id AND action_id = @action_id";
            return this.NpgsqlSearchBySingle<flow_step_action>(sql, new { flow_id, step_id, action_id });
        }

        #region 簽核紀錄

        public List<FlowStepHistoryResultModel> GetFlowHistoryView(string apply_number)
        {
            //Issue：238 -> 添加會簽層級查詢寫法
            string sql = $@"
                select * from (
                            SELECT fsh.step_id,
                                 (CASE WHEN fsh.step_id = (SELECT step_id FROM dbo.flow_step WHERE flow_id = fsh.flow_id AND step_name = N'ACKNOWLEDGE') AND fsh.acknowledge_step <> 0 THEN (SELECT fun_name AS memo FROM dbo.sys_parameters WHERE para_code = N'acknowledgeStep' AND lang_type = N'EN-US' AND func_code = CONVERT(NVARCHAR,fsh.acknowledge_step)) ELSE (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = N'EN-US' AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fsh.step_id)) END) AS step_ename,
                                 (CASE WHEN fsh.step_id = (SELECT step_id FROM dbo.flow_step WHERE flow_id = fsh.flow_id AND step_name = N'ACKNOWLEDGE') AND fsh.acknowledge_step <> 0  THEN (SELECT fun_name AS memo FROM dbo.sys_parameters WHERE para_code = N'acknowledgeStep' AND lang_type = N'ZH-TW' AND func_code = CONVERT(NVARCHAR,fsh.acknowledge_step)) ELSE (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = N'ZH-TW' AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fsh.step_id)) END) AS step_cname,
                                 fsh.step_action, fsa.display_name AS action_name, fsh.apply_sequence, 
                                 fsh.actual_signer_emplid, fsh.actual_signer_name, fsh.actual_signer_name_a, fsh.actual_signer_deptid, ee_actual.termination AS actual_signer_termination,
                                 fsh.should_signer_emplid, fsh.should_signer_name, fsh.should_signer_name_a, fsh.should_signer_deptid, ee_should.termination AS should_signer_termination,
                                 fsh.step_opinion, fsh.create_time
                                 FROM (SELECT step_id,step_action,apply_sequence,actual_signer_emplid,actual_signer_name,actual_signer_name_a,actual_signer_deptid,should_signer_emplid,should_signer_name,should_signer_name_a,should_signer_deptid,step_opinion,create_time,acknowledge_step,flow_id FROM dbo.flow_step_history WHERE apply_number = @apply_number) AS fsh 
                                 LEFT JOIN (SELECT action_id,step_id,display_name FROM dbo.flow_step_action) AS fsa ON fsh.step_action = fsa.action_id AND fsa.step_id = fsh.step_id
                                 LEFT JOIN (SELECT emplid,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS ee_actual ON fsh.actual_signer_emplid = ee_actual.emplid
                                 LEFT JOIN (SELECT emplid,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS ee_should ON fsh.should_signer_emplid = ee_should.emplid
                            UNION 
                            SELECT fsh.step_id,
                                stepNameEn as  step_ename,
                                stepNameCn AS step_cname,
                                fsh.step_action
                                ,isnull(actionName.fun_name,fsh.action_name) as action_name
                                , fsh.apply_sequence, 
                                fsh.actual_signer_emplid, fsh.actual_signer_name, fsh.actual_signer_name_a, fsh.actual_signer_deptid, ee_actual.termination AS actual_signer_termination,
                                fsh.should_signer_emplid, fsh.should_signer_name, fsh.should_signer_name_a, fsh.should_signer_deptid, ee_should.termination AS should_signer_termination,
                                fsh.step_opinion, fsh.create_time
                            FROM (SELECT 
			                            wilegal_step_id as  step_id,stepNameCn,stepNameEn ,step_action,action_name,apply_sequence,actual_signer_emplid,actual_signer_name,actual_signer_name_a,actual_signer_deptid,should_signer_emplid,should_signer_name,should_signer_name_a,should_signer_deptid,step_opinion,create_time,acknowledge_step,flow_id 
			                            FROM dbo.flow_step_history_from_wilegal fshfw  
			                            WHERE apply_number = @apply_number
	                            ) AS fsh
                            LEFT JOIN (SELECT emplid,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS ee_actual ON fsh.actual_signer_emplid = ee_actual.emplid
                            LEFT JOIN (SELECT emplid,termination FROM dbo.ps_sub_ee_lgl_vw_a) AS ee_should ON fsh.should_signer_emplid = ee_should.emplid
                            LEFT JOIN (SELECT sys_parameters.func_code ,sys_parameters.fun_name  from  sys_parameters where para_code ='WiLegalActionName' and  lang_type ='EN-US') as actionName on actionName.func_code=CONVERT(NVARCHAR,fsh.step_action)
                           ) as t ORDER BY create_time ASC;
                              ";
            return this.NpgsqlSearchByList<FlowStepHistoryResultModel>(sql, new { apply_number = apply_number, signOffStep = string.Join(',', signOffStep.Select(s => s)) });
        }

        public List<FlowStepSignerViewModel> GetFlowStepSignerView(string apply_number)
        {
            //Issue：238 -> 添加會簽層級查詢寫法
            string sql = $@"SELECT fss.*,
                            (CASE WHEN fss.step_id = (SELECT step_id FROM dbo.flow_step WHERE flow_id = fss.flow_id AND step_name = N'ACKNOWLEDGE') AND fss.acknowledge_step <> 0 THEN (SELECT fun_name AS memo FROM dbo.sys_parameters WHERE para_code = N'acknowledgeStep' AND lang_type = N'EN-US' AND func_code = CONVERT(NVARCHAR,fss.acknowledge_step)) ELSE (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = N'EN-US' AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) END) AS step_ename,
                            (CASE WHEN fss.step_id = (SELECT step_id FROM dbo.flow_step WHERE flow_id = fss.flow_id AND step_name = N'ACKNOWLEDGE') AND fss.acknowledge_step <> 0 THEN (SELECT fun_name AS memo FROM dbo.sys_parameters WHERE para_code = N'acknowledgeStep' AND lang_type = N'ZH-TW' AND func_code = CONVERT(NVARCHAR,fss.acknowledge_step)) ELSE (SELECT fun_name FROM (SELECT func_code,fun_name FROM dbo.sys_parameters AS sp WHERE lang_type = N'ZH-TW' AND EXISTS(SELECT 1 FROM (SELECT value AS para_code FROM STRING_SPLIT(@signOffStep, ',')) AS fpc WHERE fpc.para_code = sp.para_code)) AS sp WHERE sp.func_code = CONVERT(NVARCHAR,fss.step_id)) END) AS step_cname
                            FROM (SELECT rowid, flow_id, step_id, apply_number, signer_time, signer_emplid, signer_deptid, is_return, is_invitee, is_acknowledge, is_error, is_reject, return_step_id, is_sign_mcp, acknowledge_step FROM dbo.flow_step_signer WHERE apply_number = @apply_number) AS fss;";
            return this.NpgsqlSearchByList<FlowStepSignerViewModel>(sql, new { apply_number = apply_number, signOffStep = string.Join(',', signOffStep.Select(s => s)) });
        }

        //reject或withdraw後的簽核紀錄
        public List<flow_step_history> GetCurrentFlowStepHistory(string apply_number)
        {
            string sql = $@"WITH LastReject AS (
                                SELECT MAX(create_time) AS last_reject_time
                                FROM flow_step_history
                                WHERE apply_number = @apply_number
                                AND step_action IN (3, 6)
                                )
                                SELECT *
                                FROM flow_step_history
                                WHERE apply_number = @apply_number
                                AND (create_time > (SELECT last_reject_time FROM LastReject) OR (SELECT last_reject_time FROM LastReject) IS NULL)
                                ORDER BY create_time ASC;";
            return this.NpgsqlSearchByList<flow_step_history>(sql, new { apply_number });
        }

        public List<flow_step_history> GetFlowStepHistory(string apply_number)
        {
            string sql = $@"SELECT * FROM flow_step_history WHERE apply_number = @apply_number ORDER BY rowid ASC";
            return this.NpgsqlSearchByList<flow_step_history>(sql, new { apply_number });
        }

        public List<flow_step_history> GetFlowHistory(string apply_number, int step_id)
        {
            string sql = $@"SELECT * FROM flow_step_history WHERE apply_number = @apply_number AND step_id = @step_id ORDER BY rowid ASC";
            return this.NpgsqlSearchByList<flow_step_history>(sql, new { apply_number, step_id });
        }

        public bool InsertFlowStepHistory(flow_step_history history)
        {
            //Issue：238 -> 添加會簽層級插入
            string sql = $@"
                                INSERT INTO flow_step_history (flow_id, apply_number, step_id, apply_sequence, step_action, step_opinion, actual_signer_emplid, actual_signer_name, actual_signer_name_a, actual_signer_deptid, should_signer_emplid, should_signer_name, should_signer_name_a, should_signer_deptid, create_time, acknowledge_step)
                                VALUES (@flow_id, @apply_number, @step_id, @apply_sequence,@step_action, @step_opinion, @actual_signer_emplid, @actual_signer_name, @actual_signer_name_a, @actual_signer_deptid, @should_signer_emplid, @should_signer_name, @should_signer_name_a, @should_signer_deptid, @create_time, @acknowledge_step)";
            return this.ExecuteCommand(sql, history) > 0;
        }

        public bool InsertContractHistory(flow_contract_history history)
        {
            string sql = $@"INSERT INTO flow_contract_history (flow_id, apply_number, step_id, apply_sequence, step_action, step_opinion, actual_signer_emplid, actual_signer_name, actual_signer_name_a, actual_signer_deptid, should_signer_emplid, should_signer_name, should_signer_name_a, should_signer_deptid, create_time, contract_step, contract_options)
                                VALUES (@flow_id, @apply_number, @step_id, @apply_sequence,@step_action, @step_opinion, @actual_signer_emplid, @actual_signer_name, @actual_signer_name_a, @actual_signer_deptid, @should_signer_emplid, @should_signer_name, @should_signer_name_a, @should_signer_deptid, @create_time, @contract_step, @contract_options)";
            return this.ExecuteCommand(sql, history) > 0;
        }
        #endregion

        #region 待簽核人
        public bool InsertFlowStepSigner(flow_step_signer signer)
        {
            //Issue：238 -> 添加會簽層級插入
            string sql = $@"
                                INSERT INTO flow_step_signer (flow_id, step_id, apply_number, signer_time, signer_emplid, signer_deptid, is_return, return_step_id, is_invitee, is_acknowledge, is_error, is_reject, create_time, create_user, acknowledge_step)
                                VALUES (@flow_id, @step_id, @apply_number, @signer_time, @signer_emplid, @signer_deptid, @is_return, @return_step_id, @is_invitee, @is_acknowledge, @is_error, @is_reject, @create_time, @create_user,@acknowledge_step)";
            return this.ExecuteCommand(sql, signer) > 0;
        }

        public void DeleteFlowStepSigner(string apply_number)
        {
            string sql_delete = $@"DELETE FROM flow_step_signer WHERE apply_number = @apply_number";
            this.ExecuteCommand(sql_delete, new { apply_number = apply_number });
        }

        public void DeleteFlowStepSignerByEmplid(string apply_number)
        {
            string sql_agent = $@"DELETE fs
                                    FROM flow_step_signer fs
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fs.signer_emplid
                                    AND sa.auth_deptid = fs.signer_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fs.apply_number = @apply_number";
            this.ExecuteCommand(sql_agent, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp });

            string sql_delete = $@"	DELETE FROM flow_step_signer 
                                        WHERE apply_number = @apply_number 
                                        AND signer_emplid = @signer_emplid";
            this.ExecuteCommand(sql_delete, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp });
        }

        public void DeleteFlowStepSignerByEmplid(string apply_number, string emplid)
        {
            string sql_agent = $@"DELETE fs
                                    FROM flow_step_signer fs
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fs.signer_emplid
                                    AND sa.auth_deptid = fs.signer_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fs.apply_number = @apply_number";
            this.ExecuteCommand(sql_agent, new { apply_number, signer_emplid = emplid });

            string sql_delete = $@"	DELETE FROM flow_step_signer 
                                        WHERE apply_number = @apply_number 
                                        AND signer_emplid = @signer_emplid";
            this.ExecuteCommand(sql_delete, new { apply_number, signer_emplid = emplid });
        }

        public List<flow_step_signer> GetFlowStepSigner(string apply_number)
        {
            string sql = $@"SELECT * FROM flow_step_signer WHERE apply_number = @apply_number";
            return this.NpgsqlSearchByList<flow_step_signer>(sql, new { apply_number });
        }
        #endregion

        #region 關卡id
        /// <summary>
        /// 經辦人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetAgentStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AGENT'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 會簽關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetAcknowledgeStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'ACKNOWLEDGE'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 經辦人部門主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetAgnetSupervisorStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AGENT_SUPERVISOR'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 被授權正本簽屬人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetSignatoryStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AUTHORIZED_ORIGINAL_SIGNATORY'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 組織主管關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetOrganizationStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'BU_BG_CORP_FUNC_MA'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 總部法務承辦關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetWhqLowProStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'WHQ_LOW_PRO'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 總部法務行政關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetWhqLowAdminStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_WHQ_LOW_ADMIN'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// SiteGM關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetSiteGMStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_SITGM'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 法定代表人StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetCkLegalAgentStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_LEGAL_AGENT'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 總部承辦財務StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetWhqFinanceProStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_WHQ_FINANCE_PRO'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 總部承辦財務部門主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetWhqLowFinanceMaStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'WHQ_LOW_FINANCE_MA'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 合約管理人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetContractMaTaskStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CONTRACT_MA_TASK'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 總經理關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetCeoStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_CEO'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 董事長關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetPresidentStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'CK_PRESIDENT'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 總部調閱行政關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetWhqRetrieveAdminStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'WHQ_RETRIEVE_ADMIN'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 特殊主體法務行政關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetSpecialWhqLowAdminStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'SPECIAL_WHQ_LOW_ADMIN'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 各Site合約管理人關卡StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetSiteContractMaTaskStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'SITE_CONTRACT_MA_TASK'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 經辦人部門組織主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetAgentOrgSupervisorStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'AGENT_ORG_SUPERVISOR'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        /// <summary>
        /// 法務最高主管StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetLegalTopSupervisorStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'LEGAL_TOP_SUPERVISOR'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }

        #region 調閱案件原始經辦單位最高主管
        /// <summary>
        /// 調閱案件原始經辦單位最高主管
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetRetrievePicTopSupervisorStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'RETRIEVE_PIC_TOP_SUPERVISOR'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }
        #endregion

        #region 申請主體最高主管
        /// <summary>
        /// 申請主體最高主管
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetApplyEntityTopSupervisorStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'APPLY_ENTITY_TOP_SUPERVISOR'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }
        #endregion

        /// <summary>
        /// 結案StepID
        /// </summary>
        /// <param name="flow_id"></param>
        /// <returns></returns>
        public int GetEndStepid(int flow_id)
        {
            string sql = $@"SELECT step_id FROM flow_step WHERE flow_id = @flow_id AND step_name = N'END'";
            return this.NpgsqlSearchBySingle<int>(sql, new { flow_id });
        }
        #endregion

        #region 加簽
        public bool InsertFlowStepSignerInvitee(flow_step_signer_invitee invitee)
        {
            string sql = $@"INSERT INTO flow_step_signer_invitee (flow_id, step_id, apply_number, invitee_emplid, invitee_deptid, invitee_remark, is_error, create_user, create_time, invitee_type, is_acknowledge)
                                VALUES (@flow_id, @step_id, @apply_number, @invitee_emplid, @invitee_deptid, @invitee_remark, @is_error, @create_user, @create_time, @invitee_type, @is_acknowledge)";
            return this.ExecuteCommand(sql, invitee) > 0;
        }

        public bool SetInviteeSignerUsed(string apply_number, string emplid)
        {
            string sql_invitee = $@"DELETE FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_emplid = @emplid";
            return this.ExecuteCommand(sql_invitee, new { apply_number, emplid }) > 0;
        }

        #region 檢查當前加簽人員是否是會簽補簽人員
        /// <summary>
        /// 檢查當前加簽人員是否是會簽補簽人員
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="emplid"></param>
        /// <returns></returns>
        public bool CheckIsAcknowledgeInvitee(string apply_number, string emplid)
        {
            string sql = $@"SELECT COUNT(1) FROM dbo.flow_step_signer_invitee WHERE is_acknowledge = 1 AND apply_number = @apply_number AND invitee_emplid = @emplid;";
            return this.NpgsqlSearchBySingle<int>(sql, new { apply_number, emplid }) > 0;
        }
        #endregion

        public bool DeleteFlowStepSignerInvitee(string apply_number)
        {
            string sql_invitee = $@"DELETE FROM flow_step_signer_invitee WHERE apply_number = @apply_number";
            return this.ExecuteCommand(sql_invitee, new { apply_number }) > 0;
        }

        public bool DeleteFlowStepSignerInviteeSpecial(string apply_number, int sp_invite_level)
        {
            string sql_invitee = $@"DELETE FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_type = 2 AND sp_invite_level = @sp_invite_level";
            return this.ExecuteCommand(sql_invitee, new { apply_number, sp_invite_level }) > 0;
        }

        public List<flow_step_signer_invitee> GetFlowStepSignerInvitees(string apply_number)
        {
            string sql = $@"SELECT * FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_type = 1";
            return this.NpgsqlSearchByList<flow_step_signer_invitee>(sql, new { apply_number });
        }

        public List<flow_step_signer_invitee> GetFlowStepSignerInvitees_Special(string apply_number)
        {
            string sql = $@"SELECT * FROM flow_step_signer_invitee WHERE apply_number = @apply_number AND invitee_type = 2";
            return this.NpgsqlSearchByList<flow_step_signer_invitee>(sql, new { apply_number });
        }

        #region 根據申請單號驗證是否存在加簽人員數據
        /// <summary>
        /// 根據申請單號驗證是否存在加簽人員數據  Issue：200
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public bool CheckHasFlowStepSignerInvitees(string apply_number)
        {
            string sql = $@"SELECT COUNT(apply_number) FROM flow_step_signer_invitee WHERE apply_number = @apply_number;";
            return Convert.ToInt32(this.NpgsqlSearchBySingleValue(sql.ToString(), new { apply_number = apply_number })) > 0;
        }
        #endregion

        public bool UpdateFlowStepSigner_IsInvitee(string apply_number, int is_invitee)
        {
            string sql = $@"UPDATE flow_step_signer
                            SET is_invitee = @is_invitee
                            WHERE apply_number = @apply_number";
            return this.ExecuteCommand(sql, new { is_invitee, apply_number }) > 0;
        }

        public bool UpdateFormApply_Signatory(string apply_type, string apply_number, string? signatory)
        {
            string sql = string.Empty;
            if (apply_type == "C")
                sql = $@"UPDATE form_application
                            SET signatory = @signatory
                            WHERE apply_number = @apply_number";
            else if (apply_type == "O")
                sql = $@"UPDATE other_application
                            SET signatory = @signatory
                            WHERE apply_number = @apply_number";
            else return false;

            return this.ExecuteCommand(sql, new { signatory, apply_number }) > 0;
        }
        #endregion

        #region 組織主管
        public List<flow_step_signer_org> GetFlowStepSignerOrg(string apply_number)
        {
            string sql = $@"SELECT * FROM flow_step_signer_org WHERE apply_number = @apply_number ORDER BY org_level DESC";
            return this.NpgsqlSearchByList<flow_step_signer_org>(sql, new { apply_number });
        }

        public bool InsertFlowStepSignerOrg(flow_step_signer_org signerOrg)
        {
            string sql = $@"INSERT INTO flow_step_signer_org (apply_number, org_emplid, org_deptid, org_level, step_id, org_type, is_used)
                                VALUES (@apply_number, @org_emplid, @org_deptid, @org_level, @step_id, @org_type, @is_used)";
            return this.ExecuteCommand(sql, signerOrg) > 0;
        }

        public bool DeleteFlowStepSignerOrg(string apply_number)
        {
            string sql = $@"DELETE FROM flow_step_signer_org WHERE apply_number = @apply_number";
            return this.ExecuteCommand(sql, new { apply_number }) > 0;
        }

        public bool SetOrganizationSignerUsed(string apply_number)
        {
            string sql = $@"UPDATE flow_step_signer_org
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_org.apply_number = fs.apply_number
                                AND flow_step_signer_org.org_emplid = fs.signer_emplid
                                AND flow_step_signer_org.org_deptid = fs.signer_deptid
                                AND fs.apply_number = @apply_number;";
            return this.ExecuteCommand(sql, new { apply_number }) > 0;
        }

        public bool SetOrganizationSignerUsedByEmplid(string apply_number, string signer_emplid, string signer_deptid)
        {
            string sql = $@"UPDATE flow_step_signer_org
								    SET is_used = 1
                                  WHERE org_emplid = @signer_emplid
                                    AND org_deptid = @signer_deptid
                                    AND apply_number = @apply_number";
            return this.ExecuteCommand(sql, new { apply_number, signer_emplid, signer_deptid }) > 0;
        }
        #endregion

        #region 會簽
        public List<flow_step_signer_acknowledge> GetFlowStepSignerAcknowledge(string apply_number)
        {
            //Issue：443 -> 根據會簽部門層級排序
            string sql = $@"SELECT * FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number ORDER BY acknow_deptid_level DESC";
            return this.NpgsqlSearchByList<flow_step_signer_acknowledge>(sql, new { apply_number });
        }

        public List<flow_step_signer_acknowledge> GetFlowStepSignerAcknowledge(string apply_number, int step_id)
        {
            //Issue：443 -> 根據會簽部門層級排序
            string sql = $@"SELECT * FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number AND step_id = @step_id ORDER BY acknow_deptid_level DESC";
            return this.NpgsqlSearchByList<flow_step_signer_acknowledge>(sql, new { apply_number, step_id });
        }

        public bool InsertFlowStepSignerAcknowledge(flow_step_signer_acknowledge acknowledge)
        {
            //Issue：443 -> 添加會簽部門的層級存儲
            string sql = $@"
                                INSERT INTO flow_step_signer_acknowledge (apply_number, acknow_emplid, acknow_deptid, acknow_level, step_id, acknow_reason, is_used,acknow_deptid_level)
                                VALUES (@apply_number, @acknow_emplid, @acknow_deptid, @acknow_level, @step_id, @acknow_reason, @is_used,@acknow_deptid_level)";
            return this.ExecuteCommand(sql, acknowledge) > 0;
        }

        public void DeleteFlowStepSignerAcknowledge(string apply_number)
        {
            string sql_delete = $@"DELETE FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number";
            this.ExecuteCommand(sql_delete, new { apply_number = apply_number });
        }

        public void DeleteFlowStepSignerAcknowledge(string apply_number, int step_id)
        {
            string sql_delete = $@"DELETE FROM flow_step_signer_acknowledge WHERE apply_number = @apply_number AND step_id =  @step_id";
            this.ExecuteCommand(sql_delete, new { apply_number = apply_number, step_id = step_id });
        }

        #region 刪除未做過會簽補簽的人員數據
        /// <summary>
        /// 刪除未做過會簽補簽的人員數據  Issue：200
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="acknow_emplid"></param>
        public void DeleteFlowStepSignerAcknowledgeByNoUsed(string apply_number, string acknow_emplid)
        {
            string sql_delete = $@"DELETE FROM dbo.flow_step_signer_acknowledge WHERE is_used = 0 AND apply_number = @apply_number AND acknow_emplid = @acknow_emplid;";
            this.ExecuteCommand(sql_delete, new { apply_number = apply_number, acknow_emplid = acknow_emplid });
        }
        #endregion

        #region 刪除未做過會簽補簽的人員數據
        /// <summary>
        /// 刪除未做過會簽補簽的人員數據  Issue：200
        /// </summary>
        /// <param name="apply_number"></param>
        public void DeleteFlowStepSignerAcknowledgeByNoUsed(string apply_number)
        {
            string sql_delete = $@"DELETE FROM dbo.flow_step_signer_acknowledge WHERE is_used = 0 AND apply_number = @apply_number;";
            this.ExecuteCommand(sql_delete, new { apply_number = apply_number });
        }
        #endregion

        public bool SetAcknowledgeSignerUsed(string apply_number)
        {
            string sql = $@"UPDATE flow_step_signer_acknowledge
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_acknowledge.apply_number = fs.apply_number
                                AND flow_step_signer_acknowledge.acknow_emplid = fs.signer_emplid
                                AND flow_step_signer_acknowledge.acknow_deptid = fs.signer_deptid
                                AND fs.apply_number = @apply_number;";
            return this.ExecuteCommand(sql, new { apply_number }) > 0;
        }

        public bool SetAcknowledgeSignerUsedByEmplidAndAgent(string apply_number, string emplid, string deptid)
        {
            //更新代理人
            string sql_agent = $@"UPDATE fsa
								    SET fsa.is_used = 1
                                  FROM flow_step_signer_acknowledge fsa
                                  INNER JOIN flow_step_signer fs ON fs.apply_number = fsa.apply_number
                                    AND fs.signer_emplid = fsa.acknow_emplid
                                    AND fs.signer_deptid = fsa.acknow_deptid
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fsa.acknow_emplid
                                    AND sa.auth_deptid = fsa.acknow_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fsa.apply_number = @apply_number";
            this.ExecuteCommand(sql_agent, new { apply_number, signer_emplid = emplid, signer_deptid = deptid });

            //更新簽核人
            string sql = $@"UPDATE flow_step_signer_acknowledge
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_acknowledge.apply_number = fs.apply_number
                                AND flow_step_signer_acknowledge.acknow_emplid = fs.signer_emplid
                                AND flow_step_signer_acknowledge.acknow_deptid = fs.signer_deptid
                                AND fs.signer_emplid = @signer_emplid
                                AND fs.apply_number = @apply_number";
            return this.ExecuteCommand(sql, new { apply_number, signer_emplid = emplid, signer_deptid = deptid }) > 0;
        }

        public bool SetAcknowledgeSignerUsedByEmplid(string apply_number)
        {
            //更新代理人
            string sql_agent = $@"UPDATE fsa
								    SET fsa.is_used = 1
                                  FROM flow_step_signer_acknowledge fsa
                                  INNER JOIN flow_step_signer fs ON fs.apply_number = fsa.apply_number
                                    AND fs.signer_emplid = fsa.acknow_emplid
                                    AND fs.signer_deptid = fsa.acknow_deptid
                                  INNER JOIN sys_agent sa ON sa.auth_empid = fsa.acknow_emplid
                                    AND sa.auth_deptid = fsa.acknow_deptid
                                    AND CONVERT(date, sa.start_time) <= CONVERT(date, GETUTCDATE())
                                    AND CONVERT(date, sa.end_time) >= CONVERT(date, GETUTCDATE())
                                    AND sa.agent_empid = @signer_emplid
                                  WHERE fsa.apply_number = @apply_number";
            this.ExecuteCommand(sql_agent, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp, signer_deptid = MvcContext.UserInfo.current_dept });

            //更新簽核人
            string sql = $@"UPDATE flow_step_signer_acknowledge
                            SET is_used = 1
                            FROM flow_step_signer fs
                            WHERE flow_step_signer_acknowledge.apply_number = fs.apply_number
                                AND flow_step_signer_acknowledge.acknow_emplid = fs.signer_emplid
                                AND flow_step_signer_acknowledge.acknow_deptid = fs.signer_deptid
                                AND fs.signer_emplid = @signer_emplid
                                AND fs.apply_number = @apply_number";
            return this.ExecuteCommand(sql, new { apply_number, signer_emplid = MvcContext.UserInfo.current_emp, signer_deptid = MvcContext.UserInfo.current_dept }) > 0;
        }

        #region 設置為會簽人員在使用
        /// <summary>
        /// 設置為會簽人員在使用
        /// </summary>
        /// <param name="apply_number"></param>
        /// <param name="signer_emplid"></param>
        /// <param name="signer_deptid"></param>
        /// <param name="acknow_level"></param>
        /// <returns></returns>
        public bool SetAcknowledgeSignerUsedByEmplid(string apply_number, string signer_emplid, string signer_deptid, int? acknow_level = null)
        {
            string sql = $@"UPDATE dbo.flow_step_signer_acknowledge SET is_used = 1
                                     WHERE acknow_emplid = @signer_emplid
                                     AND acknow_deptid = @signer_deptid
                                     AND apply_number = @apply_number";
            //Issue：238 會簽滑關時，需要根據層級進行人員的by pass
            if (acknow_level != null)
            {
                sql += $@" AND acknow_level = @acknow_level";
            }

            return this.ExecuteCommand(sql, new { apply_number, signer_emplid, signer_deptid, acknow_level }) > 0;
        }
        #endregion

        public bool SetAcknowledgeSignerUsedByEmplid(string apply_number, string signer_emplid)
        {
            string sql = $@"UPDATE flow_step_signer_acknowledge
								    SET is_used = 1
                                  WHERE acknow_emplid = @signer_emplid
                                    AND apply_number = @apply_number";
            return this.ExecuteCommand(sql, new { apply_number, signer_emplid }) > 0;
        }
        #endregion

        public List<V_GetLegalAssign> GetLegalAssignList(string entity_id, string contract_type)
        {
            string sql = $@"SELECT * FROM V_GetLegalAssign WHERE entity_id = @entity_id AND contract_type = @contract_type";
            return this.NpgsqlSearchByList<V_GetLegalAssign>(sql, new { entity_id, contract_type });
        }

        public V_GetLegalAssign GetLegalAssignByEntity(string entity_id)
        {
            string sql = $@"SELECT TOP(1) * FROM V_GetLegalAssign WHERE entity_id = @entity_id";
            return this.NpgsqlSearchBySingle<V_GetLegalAssign>(sql, new { entity_id });
        }

        public bool ValidateEnd_File(string apply_number, int archive_purposes)
        {
            string sql = $@"SELECT COUNT(1) FROM sys_upload_file WHERE upload_type = 2 AND archive_purposes = @archive_purposes AND is_watermake = 0 AND upload_key = @apply_number";
            return this.NpgsqlSearchBySingle<int>(sql, new { apply_number, archive_purposes }) > 0;
        }

        #region 清空後送判斷原因和最終簽署人簽核關卡的選項值
        /// <summary>
        /// Issue：92 Reject、Withdraw或Return時：清空後送判斷原因和最終簽署人簽核關卡的選項值
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public bool UpdateByPassReasonAndFinalSignLevel(string apply_number)
        {
            string sql = $@"UPDATE dbo.form_application_admin SET final_sign_level = NULL,by_pass_reason = NULL WHERE apply_number = @apply_number;";
            return this.ExecuteCommand(sql, new { apply_number = apply_number }) > 0;
        }
        #endregion

        public DataTable GetUserMailContentData(string table_name, string apply_number, string current_signer, int current_step, string mail_type)
        {
            // 檢查輸入是否安全
            if (string.IsNullOrWhiteSpace(table_name))
            {
                return new DataTable();
                //throw new ArgumentException("Column name and table name cannot be null or empty.");
            }

            string baseUtcOffset = TimeZoneInfo.FindSystemTimeZoneById(MvcContext.UserInfo.time_zone).BaseUtcOffset.ToString();
            baseUtcOffset = (baseUtcOffset.IndexOf("-") > -1) ? baseUtcOffset.Substring(0, 6) : ("+" + baseUtcOffset.Substring(0, 5));

            // 使用字串插值來生成 SQL 查詢
            // 改用sql function的形式
            // 資料來源的格式紀錄在user_email_dictionary的source_type欄位，其他類別待未來擴充
            string sql = $@"SELECT * FROM {table_name}(@apply_number, @current_signer, @current_step, @baseUtcOffset)";
            //20250312pike 郵件模板查詢，稽催類型郵件查詢特殊處理
            if (table_name.Equals(AppSettingHelper.Configuration["hastenSpecialNAme"]))
                sql = $@"SELECT * FROM {table_name}(@apply_number, @mail_type)";
            DataTable res = null;
            try
            {
                res = DbAccess.Database.SqlQuery(sql, new { apply_number, current_signer, current_step, baseUtcOffset, mail_type });
            }
            catch
            {
                return new DataTable();
            }

            if (res == null) res = new DataTable();

            return res;
        }

        public V_GetAllApplication GetAllApplication(string apply_type, string apply_number)
        {
            string sql = $@"SELECT * FROM V_GetAllApplication WHERE apply_type = @apply_type AND apply_number = @apply_number";
            return this.NpgsqlSearchBySingle<V_GetAllApplication>(sql, new { apply_type, apply_number });
        }

        //!!!注意事項!!!
        //因各類型暫存單單號皆為使用者工號，此方法無根據apply_type區分，因此僅適用非暫存單
        public Elegal.Interface.Api.Common.Model.SqlSugarModels.V_GetAllApplication GetAllApplication(string apply_number)
        {
            string sql = $@"SELECT * FROM V_GetAllApplication WHERE apply_number = @apply_number";
            return this.NpgsqlSearchBySingle<V_GetAllApplication>(sql, new { apply_number });
        }

        public void ApprovedFileUpdateExpirationDate(string applyNumber, IDbContext? dbContext = null)
        {
            //sit632 新增給水印失效文檔加過期時間
            string sql = @"--轉檔文件
                            update sys_upload_file 
                            set expiry_date = DATEADD(day, (select CONVERT(int,func_code) from sys_parameters where para_code = 'transferDay' and lang_type = 'ZH-TW'), GETUTCDATE())
                            where upload_key = @applyNumber and is_watermake = 1
                            --水印文件
                            update sys_upload_file 
                            set expiry_date = DATEADD(day, (select CONVERT(int,func_code) from sys_parameters where para_code = 'transferDay' and lang_type = 'ZH-TW'), GETUTCDATE())
                            where original_file_id in (select fileid from sys_upload_file where upload_key = @applyNumber) and upload_type = N'101'";
            this.ExecuteCommand(sql, new { applyNumber = applyNumber });
        }

        //組織主管應簽核人部門現任正主管(排除緯穎)
        public List<ps_sub_ee_lgl_vw_a> GetAllORGManager(string apply_number)
        {
            string sql = @"select * from  F_GetAllORGManager(@apply_number) ";
            return this.NpgsqlSearchByList<ps_sub_ee_lgl_vw_a>(sql, new { apply_number });
        }

        //查詢郵件模板開放欄位數據
        public List<UserEmailDictionary> GetUserEmailDictionaryList(string funcModule, string mailType)
        {
            string sql = @"SELECT 
                    field_code as FieldCode, field_cname as FieldCname, field_ename as FieldEname, field_source as FieldSource,
                    field_type as FieldType, source_type as SourceType, source_para as SourcePara, has_linkurl as HasLinkurl, value as MailType
                    FROM user_email_dictionary
                    CROSS APPLY STRING_SPLIT(mail_type, ';') 
                    where field_type = @funcModule and value = @mailType";
            return this.NpgsqlSearchByList<UserEmailDictionary>(sql, new { funcModule = funcModule, mailType = mailType });
        }

        public List<UserEmailDictionary> GetUserEmailDictionaryList(List<string> funcModule, List<string> mailType)
        {
            string sql = @"SELECT 
                    field_code as FieldCode, field_cname as FieldCname, field_ename as FieldEname, field_source as FieldSource,
                    field_type as FieldType, source_type as SourceType, source_para as SourcePara, has_linkurl as HasLinkurl, value as MailType
                    FROM user_email_dictionary
                    CROSS APPLY STRING_SPLIT(mail_type, ';') 
                    where field_type in (@funcModule) and value in (@mailType)";

            return SqlSugarHelper.Db.Ado.SqlQuery<UserEmailDictionary>(sql, new { funcModule = funcModule, mailType = mailType });
        }


        #region 根據申請單號查詢所有加簽/特殊加簽人員數據(FOR JOB默認台北時區)
        /// <summary>
        /// 根據申請單號查詢所有加簽/特殊加簽人員數據(FOR JOB默認台北時區)
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        internal List<flow_step_signer> GetFlowStepSignerAndAgent(string apply_number)
        {
            string sql = $@"SELECT signer_emplid ,signer_deptid  FROM flow_step_signer WHERE apply_number = @apply_number
                            UNION 
                            select agent_empid , auth_deptid from sys_agent sa 
                            where EXISTS (select 1 from flow_step_signer
						                            where apply_number = @apply_number
						                            and signer_emplid = sa.auth_empid 
						                            and signer_deptid = sa.auth_deptid  
						                            )
                            and SWITCHOFFSET(start_time,'+08:00') <= SWITCHOFFSET(GETUTCDATE() ,'+08:00') 
                            and SWITCHOFFSET(end_time,'+08:00') > SWITCHOFFSET(GETUTCDATE() ,'+08:00') ";
            return this.NpgsqlSearchByList<flow_step_signer>(sql, new { apply_number });
        }
        #endregion

        #region 根據申請單號查詢所有加簽/特殊加簽人員數據(FOR JOB默認台北時區)
        /// <summary>
        /// 根據申請單號查詢所有加簽/特殊加簽人員數據(FOR JOB默認台北時區)
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public List<flow_step_signer_invitee> GetFlowStepSignerAllInviteesAndAgent(string apply_number)
        {
            string sql = $@"SELECT invitee_emplid ,invitee_deptid  FROM flow_step_signer_invitee WHERE apply_number = @apply_number
                            UNION 
                            select agent_empid , auth_deptid from sys_agent sa 
                            where EXISTS (select 1 from flow_step_signer_invitee
						                            where apply_number = @apply_number
						                            and invitee_emplid = sa.auth_empid 
						                            and invitee_deptid = sa.auth_deptid  
						                            )
                            and SWITCHOFFSET(start_time,'+08:00') <= SWITCHOFFSET(GETUTCDATE() ,'+08:00') 
                            and SWITCHOFFSET(end_time,'+08:00') > SWITCHOFFSET(GETUTCDATE() ,'+08:00') ";
            return this.NpgsqlSearchByList<flow_step_signer_invitee>(sql, new { apply_number });
        }
        #endregion

        public bool CheckIsAcknowledgeStepID(int current_step, int flow_id)
        {
            return current_step == GetAcknowledgeStepid(flow_id);
        }

        #region 獲取簽核歷程中最後一個非bypass的關卡簽核ID
        /// <summary>
        /// 獲取簽核歷程中最後一個非bypass的關卡簽核ID
        /// </summary>
        /// <param name="apply_number"></param>
        /// <returns></returns>
        public int GetFinalSignOffStepID(string apply_number)
        {
            string sql = $@"
                           SELECT step_id FROM (SELECT apply_number,step_id FROM dbo.flow_step_history WHERE step_action > 0 AND apply_number = @apply_number AND rowid = (SELECT MAX(rowid) FROM dbo.flow_step_history WHERE step_action > 0 AND apply_number = @apply_number)) AS fsh;";
            return this.NpgsqlSearchBySingle<int>(sql, new { apply_number });
        }
        #endregion
    }
}
