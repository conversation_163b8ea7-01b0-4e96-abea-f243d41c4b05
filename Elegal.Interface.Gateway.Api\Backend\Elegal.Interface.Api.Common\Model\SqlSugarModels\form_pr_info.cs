﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///申請單 -> PMCS核准的PR單數據
    ///</summary>
    [SugarTable("form_pr_info")]
    public partial class form_pr_info
    {
           public form_pr_info(){


           }
           /// <summary>
           /// Desc:申請單號，與 form_application.apply_number 關聯
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:PR單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string pr_no {get;set;} = null!;

           /// <summary>
           /// Desc:PR類型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pr_type {get;set;}

           /// <summary>
           /// Desc:PR備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pr_remark {get;set;}

           /// <summary>
           /// Desc:PR幣別
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? currency {get;set;}

           /// <summary>
           /// Desc:PR單金額
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? amount {get;set;}

           /// <summary>
           /// Desc:PR單掛帳部門Code
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string project_code {get;set;} = null!;

           /// <summary>
           /// Desc:PR單掛帳部門Name
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? project_name {get;set;}

           /// <summary>
           /// Desc:PR單掛帳部門ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:PR單掛帳部門佔比
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? percentage {get;set;}

           /// <summary>
           /// Desc:PR 單據連結
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pr_url {get;set;}

    }
}
