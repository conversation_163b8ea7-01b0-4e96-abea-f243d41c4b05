﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///關企申請 -> 財務資訊
    ///</summary>
    [SugarTable("enterprise_application_finance")]
    public partial class enterprise_application_finance
    {
           public enterprise_application_finance(){


           }
           /// <summary>
           /// Desc:申請單號，與 enterprise_application.apply_number 關聯
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:金額
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? amount {get;set;}

           /// <summary>
           /// Desc:幣別；sys_parameters.para_code = N'currency'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? currency {get;set;}

           /// <summary>
           /// Desc:應收/付款；sys_parameters.para_code = N'accountType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? account_type {get;set;}

           /// <summary>
           /// Desc:payment_term
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? payment_term {get;set;}

           /// <summary>
           /// Desc:稅金；sys_parameters.para_code = N'taxType'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? taxes_type {get;set;}

           /// <summary>
           /// Desc:匯率類型，默認為M
           /// Default:M
           /// Nullable:True
           /// </summary>           
           public string? exchange_rate_type {get;set;}

           /// <summary>
           /// Desc:獲取匯率時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? exchange_rate_date {get;set;}

           /// <summary>
           /// Desc:需要轉換的匯率，默認為金額乘以匯率
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? exchange_rate {get;set;}

           /// <summary>
           /// Desc:換算後金額，計算方式：金額乘以匯率
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? exchange_amount {get;set;}

           /// <summary>
           /// Desc:稅金其他選項時，需要填寫
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_taxes_type {get;set;}

    }
}
