﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///經辦人員轉單歷程
    ///</summary>
    [SugarTable("transfer_pic_history")]
    public partial class transfer_pic_history
    {
           public transfer_pic_history(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:轉單主表信息
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int transfer_id {get;set;}

           /// <summary>
           /// Desc:申請單號，與 申請單.apply_number 關聯
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:申請單填單人工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_fill_emplid {get;set;}

           /// <summary>
           /// Desc:申請單經辦人工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_pic_emplid {get;set;}

           /// <summary>
           /// Desc:申請單經辦人部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_pic_deptid {get;set;}

           /// <summary>
           /// Desc:申請單現任聯絡人工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_incumbent_emplid {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
