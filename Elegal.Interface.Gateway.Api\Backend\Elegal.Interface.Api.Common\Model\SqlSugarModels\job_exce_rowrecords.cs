﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///同步JOB每笔数据执行情况记录
    ///</summary>
    [SugarTable("job_exce_rowrecords")]
    public partial class job_exce_rowrecords
    {
           public job_exce_rowrecords(){


           }
           /// <summary>
           /// Desc:主键
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int ID {get;set;}

           /// <summary>
           /// Desc:1.0 的批次號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime batch_date {get;set;}

           /// <summary>
           /// Desc:该条数据同步的执行时间
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? exec_date {get;set;}

           /// <summary>
           /// Desc:異動的類型：	1: 新增結案/作廢的申請單	2:附檔	3:歸檔紀錄	4.修改表單
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ChangedType {get;set;}

           /// <summary>
           /// Desc:單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ApplyNumber {get;set;}

           /// <summary>
           /// Desc:1.0 裡邊申請單一級類型：	1:合約申請	2:文件申請	3:資料建檔	4:關企建檔	5:其他申請	6:特殊申請
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? form_type_1 {get;set;}

           /// <summary>
           /// Desc:1.0 裡邊申請單二級類型	1:一般合約	2:資金合約	3:其他申請A類	4:其他申請B類	5:其他申請C類	6:其他申請D類	7:其他申請E類
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? form_type_2 {get;set;}

           /// <summary>
           /// Desc:執行狀態
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? exec_status {get;set;}

    }
}
