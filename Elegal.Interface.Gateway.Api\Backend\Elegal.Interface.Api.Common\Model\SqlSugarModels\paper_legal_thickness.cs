﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///legal1.0 過厚區紙本資料原始數據 + 112年度數據
    ///</summary>
    [SugarTable("paper_legal_thickness")]
    public partial class paper_legal_thickness
    {
           public paper_legal_thickness(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? number {get;set;}

           /// <summary>
           /// Desc:年度
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? annual {get;set;}

           /// <summary>
           /// Desc:合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_number {get;set;}

           /// <summary>
           /// Desc:紙本編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_code {get;set;}

           /// <summary>
           /// Desc:紙本名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_name {get;set;}

           /// <summary>
           /// Desc:紙本類型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_type {get;set;}

           /// <summary>
           /// Desc:位置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? paper_position {get;set;}

           /// <summary>
           /// Desc:正本歸檔狀態
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? filed_type {get;set;}

           /// <summary>
           /// Desc:歸檔日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? filed_date {get;set;}

           /// <summary>
           /// Desc:eLegal1.0歸檔備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? filed_remark {get;set;}

           /// <summary>
           /// Desc:印花稅
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? stamp_duty {get;set;}

           /// <summary>
           /// Desc:eLibrary基本資料備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? elib_remark {get;set;}

           /// <summary>
           /// Desc:資料來源
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? data_db {get;set;}

           /// <summary>
           /// Desc:插入日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? insert_date {get;set;}

           /// <summary>
           /// Desc:主體簡稱(我方主體)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? entity {get;set;}

    }
}
