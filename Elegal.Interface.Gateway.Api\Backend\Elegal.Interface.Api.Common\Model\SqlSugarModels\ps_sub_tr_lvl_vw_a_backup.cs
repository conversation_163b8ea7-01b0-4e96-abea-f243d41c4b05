﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("ps_sub_tr_lvl_vw_a_backup")]
    public partial class ps_sub_tr_lvl_vw_a_backup
    {
           public ps_sub_tr_lvl_vw_a_backup(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? tree_level_num {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string tree_level {get;set;} = null!;

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string descr {get;set;} = null!;

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string descr_a {get;set;} = null!;

    }
}
