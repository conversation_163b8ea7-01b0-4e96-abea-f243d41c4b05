﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///組織表
    ///</summary>
    [SugarTable("ps_sub_og_lgl_vw_a")]
    public partial class ps_sub_og_lgl_vw_a
    {
           public ps_sub_og_lgl_vw_a(){


           }
           /// <summary>
           /// Desc:部門代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:部門中文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string descr {get;set;} = null!;

           /// <summary>
           /// Desc:部門英文名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string descr_a {get;set;} = null!;

           /// <summary>
           /// Desc:部門主管工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string manager_id {get;set;} = null!;

           /// <summary>
           /// Desc:支薪地
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string sal_location_a {get;set;} = null!;

           /// <summary>
           /// Desc:部門所屬公司
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company {get;set;} = null!;

           /// <summary>
           /// Desc:部門層級
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal tree_level_num {get;set;}

           /// <summary>
           /// Desc:上層部門代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string uporg_code_a {get;set;} = null!;

           /// <summary>
           /// Desc:該部門之前的部門代碼，組織異動後的值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? deptid_old_a {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? plant_id_a {get;set;}

           /// <summary>
           /// Desc:BO\BU 業務單位
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? bu {get;set;}

           /// <summary>
           /// Desc:事業群
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? bg {get;set;}

           /// <summary>
           /// Desc:部門建立日期
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_date {get;set;}

    }
}
