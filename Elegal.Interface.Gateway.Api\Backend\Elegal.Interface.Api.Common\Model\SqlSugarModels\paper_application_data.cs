﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///紙本申請單資料表(主表)
    ///</summary>
    [SugarTable("paper_application_data")]
    public partial class paper_application_data
    {
           public paper_application_data(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int application_id {get;set;}

           /// <summary>
           /// Desc:申請單號，唯一
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:我方主體，對應申請單的我方主體
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? my_entity_id {get;set;}

           /// <summary>
           /// Desc:他方，對應申請單的他方
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? party_a {get;set;}

           /// <summary>
           /// Desc:經辦人，對應申請單的經辦人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_emplid {get;set;}

           /// <summary>
           /// Desc:經辦人部門，對應申請單的經辦人部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_deptid {get;set;}

           /// <summary>
           /// Desc:合約編號，對應申請單的合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:合約名稱，對應申請單的合約名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_name {get;set;}

           /// <summary>
           /// Desc:是否有無紙本，1：有；0：無
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public bool? having_paper {get;set;}

           /// <summary>
           /// Desc:申請單紙本確認狀態	參數表  para_code = lib_applicationStatus	從1.0存在的數據默認為01	走正常申請單的數據默認為02
           /// Default:02
           /// Nullable:False
           /// </summary>           
           public string application_status {get;set;} = null!;

           /// <summary>
           /// Desc:是否為1.0操作過來的數據，1：是；0：不是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_old_application {get;set;}

           /// <summary>
           /// Desc:紙本確認備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? application_remarks {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:是否建立紙本清單；0：否(專責單位管理)；1：是
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public bool? is_paper_detail {get;set;}

    }
}
