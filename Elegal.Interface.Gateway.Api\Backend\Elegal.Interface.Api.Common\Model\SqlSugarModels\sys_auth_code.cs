﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///授權碼對照表
    ///</summary>
    [SugarTable("sys_auth_code")]
    public partial class sys_auth_code
    {
           public sys_auth_code(){


           }
           /// <summary>
           /// Desc:工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string empid {get;set;} = null!;

           /// <summary>
           /// Desc:授權碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string a_code {get;set;} = null!;

           /// <summary>
           /// Desc:狀態
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int state {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
