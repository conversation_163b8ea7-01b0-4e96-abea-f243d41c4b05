﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///????????????,?????????
    ///</summary>
    [SugarTable("V_GetAllOtherPartyByJson")]
    public partial class V_GetAllOtherPartyByJson
    {
           public V_GetAllOtherPartyByJson(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? other_party {get;set;}

    }
}
