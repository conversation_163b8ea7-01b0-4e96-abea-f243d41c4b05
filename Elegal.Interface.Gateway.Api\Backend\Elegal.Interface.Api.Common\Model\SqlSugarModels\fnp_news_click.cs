﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///公告管理點擊對照
    ///</summary>
    [SugarTable("fnp_news_click")]
    public partial class fnp_news_click
    {
           public fnp_news_click(){


           }
           /// <summary>
           /// Desc:序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:員工工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string emplid {get;set;} = null!;

           /// <summary>
           /// Desc:公告id
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public int newid {get;set;}

           /// <summary>
           /// Desc:操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

    }
}
