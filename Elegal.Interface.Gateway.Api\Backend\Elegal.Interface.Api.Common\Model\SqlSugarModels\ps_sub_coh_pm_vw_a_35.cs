﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///
    ///</summary>
    [SugarTable("ps_sub_coh_pm_vw_a_35")]
    public partial class ps_sub_coh_pm_vw_a_35
    {
           public ps_sub_coh_pm_vw_a_35(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? deptid {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? manager_id {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public decimal? seq_no {get;set;}

    }
}
