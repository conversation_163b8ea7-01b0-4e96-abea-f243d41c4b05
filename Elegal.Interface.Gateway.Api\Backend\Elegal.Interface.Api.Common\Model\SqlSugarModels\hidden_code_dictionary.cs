﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///隱碼字典
    ///</summary>
    [SugarTable("hidden_code_dictionary")]
    public partial class hidden_code_dictionary
    {
           public hidden_code_dictionary(){


           }
           /// <summary>
           /// Desc:隱碼欄位code
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string hidden_code {get;set;} = null!;

           /// <summary>
           /// Desc:隱碼欄位預留開始字符長度
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int hidden_start {get;set;}

           /// <summary>
           /// Desc:隱碼欄位預留結束字符長度，如果為負數，則表示從起始下標一直到文本的總長度
           /// Default:-1
           /// Nullable:False
           /// </summary>           
           public int hidden_end {get;set;}

           /// <summary>
           /// Desc:隱碼值
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string hidden_value {get;set;} = null!;

           /// <summary>
           /// Desc:隱碼欄位中文
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string hidden_name {get;set;} = null!;

           /// <summary>
           /// Desc:是否為特殊字段：	0 -> 正常文本	1 -> json 轉換 List<object>	2 -> 日期轉換為string類型
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int hidden_special {get;set;}

           /// <summary>
           /// Desc:需要引用特殊字段名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? special_name {get;set;}

           /// <summary>
           /// Desc:是否需要隱碼；0：不需要，1：需要
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_used {get;set;}

           /// <summary>
           /// Desc:隱碼類型	all：全站查詢介面(極機密)	oe：OB匯出清單(極機密)	od：O明細列表(不區分機密等級)	mail：郵件隱碼規則	modify：修改表單內容隱碼規則
           /// Default:all
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string hidden_type {get;set;} = null!;

    }
}
