﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///申請單加簽人員數據
    ///</summary>
    [SugarTable("flow_step_signer_invitee")]
    public partial class flow_step_signer_invitee
    {
           public flow_step_signer_invitee(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:流程代號	1-一般合約	2-資金合約	3-人力資源合約
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int flow_id {get;set;}

           /// <summary>
           /// Desc:簽核流程id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int step_id {get;set;}

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:加簽人員工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string invitee_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:加簽人員部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? invitee_deptid {get;set;}

           /// <summary>
           /// Desc:是否有錯；0：無錯；1：有錯
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_error {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:加簽類型；1：一般加簽；2：特殊加簽
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int invitee_type {get;set;}

           /// <summary>
           /// Desc:特殊加簽原因 (申請人/經辦人)；sys_parameters.para_code = N'spInviteByPic'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_sp_reason {get;set;}

           /// <summary>
           /// Desc:特殊加簽原因 (法務)；sys_parameters.para_code = N'spInviteByLegal'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? legal_sp_reason {get;set;}

           /// <summary>
           /// Desc:特殊加簽備註 (申請人/經辦人)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? pic_sp_reamrk {get;set;}

           /// <summary>
           /// Desc:特殊加簽備註 (法務)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? legal_sp_reamrk {get;set;}

           /// <summary>
           /// Desc:特殊加簽人員簽核層級	1：申請人/經辦人/現任聯絡人	2：法務
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? sp_invite_level {get;set;}

           /// <summary>
           /// Desc:一般加簽原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? invitee_remark {get;set;}

           /// <summary>
           /// Desc:特殊加簽設定；sys_parameters.para_code = N'spInviteSetting'
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sp_invitee_setting {get;set;}

           /// <summary>
           /// Desc:是否為會簽補簽人員
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_acknowledge {get;set;}

           /// <summary>
           /// Desc:是否有被MCP簽核；0：沒有；1：有
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_sign_mcp {get;set;}

    }
}
