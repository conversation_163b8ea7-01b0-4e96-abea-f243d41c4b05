﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///簽核關卡 ->  依組織部門層級
    ///</summary>
    [SugarTable("flow_step_type_org_level")]
    public partial class flow_step_type_org_level
    {
           public flow_step_type_org_level(){


           }
           /// <summary>
           /// Desc:關卡代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public int step_id {get;set;}

           /// <summary>
           /// Desc:是否CoHead簽核：	1：簽核	0：不簽核
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool cohead_sign {get;set;}

           /// <summary>
           /// Desc:開始層級種類(1.0邏輯)：	1-指定開始層級	2-部門層級加上開始層級修正(Phase1暫不實現)	3-部門層級但不低於開始層級(Phase1暫不實現)	4-部門層級但不高於開始層級(Phase1暫不實現)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string level_strat_type {get;set;} = null!;

           /// <summary>
           /// Desc:結束層級種類(1.0邏輯)：	1-指定結束層級	2-部門層級加上結束層級修正(Phase1暫不實現)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string level_end_type {get;set;} = null!;

           /// <summary>
           /// Desc:開始層級：	若為1-指定層級，則記錄指定開始層級	若為2-部門層級加上開始層級修正，則記錄修正值（正負零皆可）	若為3-部門層級但不低於開始層級，則記錄最低開始層級	若為4-部門層級但不高於開始層級，則記錄最高開始層級
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int org_level_strat {get;set;}

           /// <summary>
           /// Desc:結束層級：	若為1-指定層級，則記錄指定結束層級	若為2-部門層級加上結束層級修正，則記錄修正值（正負零皆可）
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int org_level_end {get;set;}

           /// <summary>
           /// Desc:參考關卡
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ref_step_id {get;set;}

    }
}
