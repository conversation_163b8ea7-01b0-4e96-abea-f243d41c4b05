﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///多語言對應資料
    ///</summary>
    [SugarTable("sys_multilingual_assets")]
    public partial class sys_multilingual_assets
    {
           public sys_multilingual_assets(){


           }
           /// <summary>
           /// Desc:序號，組件，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int id {get;set;}

           /// <summary>
           /// Desc:原來的值
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string language_key {get;set;} = null!;

           /// <summary>
           /// Desc:語言
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string language_code {get;set;} = null!;

           /// <summary>
           /// Desc:多語言對應的值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? assets {get;set;}

    }
}
