﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///我方公司/主體管理流程對照表
    ///</summary>
    [SugarTable("sys_entity_manage_step")]
    public partial class sys_entity_manage_step
    {
           public sys_entity_manage_step(){


           }
           /// <summary>
           /// Desc:主鍵，序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:主體id，唯一
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:當前流程id(對應sys_parameters.func_code)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int entity_stepid {get;set;}

           /// <summary>
           /// Desc:下一個流程id對應sys_parameters.func_code)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int entity_next_stepid {get;set;}

           /// <summary>
           /// Desc:創建人，操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
