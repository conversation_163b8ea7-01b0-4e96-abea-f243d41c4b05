﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///關企部門
    ///</summary>
    [SugarTable("affiliate_dept")]
    public partial class affiliate_dept
    {
           public affiliate_dept(){


           }
           /// <summary>
           /// Desc:部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string aff_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:部門code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_deptcode {get;set;} = null!;

           /// <summary>
           /// Desc:公司code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_company_code {get;set;} = null!;

           /// <summary>
           /// Desc:中文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_dept_cname {get;set;} = null!;

           /// <summary>
           /// Desc:英文名
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? aff_dept_ename {get;set;}

           /// <summary>
           /// Desc:部門狀態，1：啟用；0：停用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string aff_dept_status {get;set;} = null!;

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
