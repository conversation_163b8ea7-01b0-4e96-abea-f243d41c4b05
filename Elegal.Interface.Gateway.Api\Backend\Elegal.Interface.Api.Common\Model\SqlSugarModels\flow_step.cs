﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///申請單簽核流程
    ///</summary>
    [SugarTable("flow_step")]
    public partial class flow_step
    {
           public flow_step(){


           }
           /// <summary>
           /// Desc:關卡ID，對應 sys_parameters.para_code IN (N'acknowledgeStep',N'capitalContractStep',N'generalContractStep',N'hrContractStep')
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public int step_id {get;set;}

           /// <summary>
           /// Desc:流程代號	1-一般合約	2-資金合約	3-人力資源合約
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int flow_id {get;set;}

           /// <summary>
           /// Desc:關卡名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? step_name {get;set;}

           /// <summary>
           /// Desc:關卡生效日期
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime begin_time {get;set;}

           /// <summary>
           /// Desc:關卡種類	1-直屬主管簽核	2-組織部門層級簽核	3-程式指定	4-流程開始關卡(Virtual step for recording)	5-流程結束關卡(Virtual step for recording)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? step_type {get;set;}

           /// <summary>
           /// Desc:簽核種類	1-關卡只需一人簽	2-關卡所有人都要簽 3-首位決
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? approval_type {get;set;}

           /// <summary>
           /// Desc:下一關卡
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? next_step_id {get;set;}

           /// <summary>
           /// Desc:關卡備註說明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? memo {get;set;}

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:是否為動態關卡；1：是；0：否
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_dynamic_step {get;set;}

    }
}
