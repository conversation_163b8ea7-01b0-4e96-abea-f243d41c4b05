﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///簽核流程預存儲
    ///</summary>
    [SugarTable("flow_step_process")]
    public partial class flow_step_process
    {
           public flow_step_process(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public long rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與申請單對應
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:簽核關卡
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int step_id {get;set;}

           /// <summary>
           /// Desc:簽核人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string signer_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:簽核部門
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string signer_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:簽核部門層級
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? dept_level {get;set;}

           /// <summary>
           /// Desc:簽核人員中文
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? signer_c_name {get;set;}

           /// <summary>
           /// Desc:簽核人員英文
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? signer_e_name {get;set;}

           /// <summary>
           /// Desc:簽核人員離職日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? signer_quit_time {get;set;}

           /// <summary>
           /// Desc:關卡英文
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? current_e_name {get;set;}

           /// <summary>
           /// Desc:關卡中文
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? current_c_name {get;set;}

           /// <summary>
           /// Desc:關卡名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string step_name {get;set;} = null!;

           /// <summary>
           /// Desc:下一關卡步驟
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? next_step_id {get;set;}

           /// <summary>
           /// Desc:關卡類型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? step_type {get;set;}

           /// <summary>
           /// Desc:簽核類型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? approval_type {get;set;}

           /// <summary>
           /// Desc:簽核層級
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? approve_level {get;set;}

           /// <summary>
           /// Desc:會簽層級
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ac_type_level {get;set;}

           /// <summary>
           /// Desc:會簽類型
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ac_type {get;set;}

           /// <summary>
           /// Desc:會簽中文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ac_type_e_name {get;set;}

           /// <summary>
           /// Desc:會簽英文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ac_type_c_name {get;set;}

           /// <summary>
           /// Desc:會簽步驟
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ac_step {get;set;}

           /// <summary>
           /// Desc:會簽步驟層級
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? ac_step_level {get;set;}

           /// <summary>
           /// Desc:會簽步驟中文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ac_step_e_name {get;set;}

           /// <summary>
           /// Desc:會簽步驟英文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ac_step_c_name {get;set;}

           /// <summary>
           /// Desc:是否動態關卡
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_dynamic_step {get;set;}

           /// <summary>
           /// Desc:是否會簽
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_acknowledge {get;set;}

           /// <summary>
           /// Desc:創建者
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:申請單類型(一級)；sys_parameters.para_code = N'applicationType'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_type {get;set;} = null!;

           /// <summary>
           /// Desc:申請類別(二級)；sys_parameters.para_code = N'formType_O,formType_C,formType_A'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string form_type {get;set;} = null!;

    }
}
