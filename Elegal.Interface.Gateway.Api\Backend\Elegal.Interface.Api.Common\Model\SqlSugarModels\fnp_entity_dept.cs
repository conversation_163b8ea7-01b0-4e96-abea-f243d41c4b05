﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///主體部門對照表
    ///</summary>
    [SugarTable("fnp_entity_dept")]
    public partial class fnp_entity_dept
    {
           public fnp_entity_dept(){


           }
           /// <summary>
           /// Desc:部門id
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:主體id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:有效期開始日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? open_date_start {get;set;}

           /// <summary>
           /// Desc:有效期結束日期
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? open_date_end {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
