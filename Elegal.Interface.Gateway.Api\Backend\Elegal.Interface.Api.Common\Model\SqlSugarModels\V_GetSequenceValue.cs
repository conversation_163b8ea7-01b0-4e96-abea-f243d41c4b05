﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///??????????
    ///</summary>
    [SugarTable("V_GetSequenceValue")]
    public partial class V_GetSequenceValue
    {
           public V_GetSequenceValue(){


           }
           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? sequence_value {get;set;}

    }
}
