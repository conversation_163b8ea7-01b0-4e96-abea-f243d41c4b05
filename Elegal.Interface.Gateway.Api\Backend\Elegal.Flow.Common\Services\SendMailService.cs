﻿using Dm.util;
using Elegal.Flow.Common.InternalModel;
using Elegal.Flow.Common.Repository;
using Elegal.Flow.Common.Repository.FlowStep;
using Elegal.Flow.Common.Repository.FormApply;
using Elegal.Flow.Common.Repository.OtherApply;
using Elegal.Flow.Common.Services.FlowStep;
using Elegal.Flow.Common.Services.PaperTracking;
using Elegal.Interface.Api.Common.Control.Filter;
using Elegal.Interface.Api.Common.Dots;
using Elegal.Interface.Api.Common.FuncHelper;
using Elegal.Interface.Api.Common.Model.DBModel;
using Elegal.Interface.Api.Common.Model.DBModel.flow;
using Elegal.Interface.Api.Common.Model.DBModel.FlowStep;
using Elegal.Interface.Api.Common.Model.DBModel.formApply;
using Elegal.Interface.Api.Common.Model.DBModel.otherApply;
using Elegal.Interface.Api.Common.Model.Enum;
using Elegal.Interface.Api.Common.Model.ParaModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi;
using Elegal.Interface.Api.Common.Model.ResultModel.FlowApi.FlowStep;
using Elegal.Interface.Api.Common.Model.ResultModel.HRApi;
using Elegal.Interface.Api.Common.Model.ViewModel.FlowApi.FormApply;
using Elegal.Interface.Api.Common.Repository;
using Elegal.Interface.ApiData.Service.FuncService;
using Elegal.Interface.ApiData.Service.Model.DbModel;
using Elegal.Interface.ApiData.Service.Model.QueryCondition;
using Elegal.Interface.ApiData.Service.Repository;
using Elegal.Orm.Dtos;
using Newtonsoft.Json;
using System.Data;
using System.Text;

namespace Elegal.Flow.Common.Services
{
    public class SendMailService
    {
        private static readonly FlowStepRepository _flowRepository = new FlowStepRepository();
        private static readonly FlowStepProcessRepository _processRepository = new FlowStepProcessRepository();
        private static readonly psRepository _psRepository = new psRepository();
        private static readonly OtherApplyRepository _otherApplyRepository = new OtherApplyRepository();
        private static readonly FormApplyRepository _formApplyRepository = new FormApplyRepository();
        private static readonly ApplicationPermissionRepository _applyPermissionRepository = new ApplicationPermissionRepository();
        private static readonly SendMailRepository _repository = new SendMailRepository();

        #region 合約郵件

        /// <summary>
        /// 寄信
        /// </summary>
        public static bool SendMail(string apply_type, string apply_number, string current_signer, int current_step, List<ApplicationApproveProcess> currentStepList, List<MailTypeUtils> mailTypeList, List<MailRecipientResultModel> inputReceiverList = null)
        {
            try
            {
                if (mailTypeList == null || mailTypeList.Count == 0) return false;

                var sendMailService = new SendMailService();
                var sysEmailContentList = sendMailService.GetMailContentList(apply_number, mailTypeList, current_signer, current_step);
                // TODO: 后续可能需要传值过来, 而不是每次都去查数据库
                var application = _flowRepository.GetAllApplication(apply_number);

                foreach (MailTypeUtils mail_type in mailTypeList)
                {
                    var sysEmailContent = sysEmailContentList.FirstOrDefault(x => x.mail_type == mail_type.toString());
                    if (sysEmailContent == null) return false;

                    SysEmail se = new SysEmail();

                    #region 默認參數

                    //郵件發送人員                    
                    var receiverList = sendMailService.GetReceiverV2(application, apply_type,
                        sysEmailContent.mail_re_type, inputReceiverList);
                    se.EReceiver = sendMailService.GetMailAddressV2(receiverList, sysEmailContent.is_re_agent);
                    //拼接郵件抄送人員
                    var ccList = sendMailService.GetReceiverV2(application, apply_type, sysEmailContent.mail_cc_type,
                        inputReceiverList);
                    se.ECc = sendMailService.GetMailAddressV2(ccList, sysEmailContent.is_re_agent);
                    //郵件標題
                    se.ESubject = sysEmailContent.mail_subject;
                    //郵件內容
                    se.EContent = sysEmailContent.mail_content;
                    se.ESendtime = DateTime.UtcNow;
                    se.ESendnum = 0;
                    se.EIssend = (int)YesOrNoUtils.No;
                    se.EType = sysEmailContent.mail_type;

                    #endregion

                    //發送郵件
                    if (!string.IsNullOrEmpty(se.EReceiver) || !string.IsNullOrEmpty(se.ECc))
                        SysEmailDataService.Create(se);
                }

                return true;
            }
            catch (Exception ex)
            {
                return false;
            }
        }

        /// <summary>
        /// 取得郵件模板
        /// </summary>
        public static UserEmailContent GetMailContent(string apply_number, MailTypeUtils mail_type, string current_signer, int current_step)
        {
            UserEmailContentDataRepository emailContentRepository = new UserEmailContentDataRepository();
            UserEmailDictionaryDataRepository emailDictionaryRepository = new UserEmailDictionaryDataRepository();

            List<UserEmailContent> userEmailContentList = emailContentRepository.Query(new UserEmailContentQueryCondition()
            {
                MailType = mail_type.ToString()
            });

            if (userEmailContentList == null || userEmailContentList.Count == 0) return null;

            UserEmailContent userEmailContent = userEmailContentList.FirstOrDefault();
            if (userEmailContent.IsReAgent == null) userEmailContent.IsReAgent = 1;


            //List<UserEmailDictionary> userEmailDictionaries = emailDictionaryRepository.Query(new UserEmailDictionaryQueryCondition()
            //{
            //    FieldType = userEmailContent.FuncModule
            //});
            //20250217郵件開放欄位取值優化Pike
            List<UserEmailDictionary> userEmailDictionaries = _flowRepository.GetUserEmailDictionaryList(userEmailContent.FuncModule, mail_type.ToString());



            string confiden_level = string.Empty;

            List<MailContentDataResultModel> mailContentDataList = new List<MailContentDataResultModel>();



            #region 處理需要根據單號查詢的字段
            //字段根据数据来源分组，同一个来源一次性查询，避免多次查询
            var group = userEmailDictionaries.Where(e => !string.IsNullOrWhiteSpace(e.FieldSource)).GroupBy(e => new { e.FieldSource, e.SourceType }).ToList();
            foreach (var groupItem in group)//循環組
            {
                //一次性查詢出所有的列
                //20250312pike 郵件模板查詢，稽催類型郵件查詢特殊處理
                var dt = _flowRepository.GetUserMailContentData(groupItem.Key.FieldSource, apply_number, current_signer, current_step, mail_type.ToString());

                var row = dt.GetRow(0); //郵件內容與標題的字段直接取第一筆數據 2025/1/15早會說的
                if (row == null) continue;//沒查到一條數據，則直接跳過這個組
                //這裡默認視為只有一筆直接為字段賦值
                foreach (var item in groupItem)
                {

                    if (item.FieldCode == "elegal_code")
                    {
                        string legalUrl = AppSettingHelper.Configuration["LegalUrl"] ?? "";
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_zh",
                            column_value = "elegal.wistron.com"
                        });
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_en",
                            column_value = "elegal.wistron.com"
                        });
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_link",
                            column_value = legalUrl
                        });

                        continue;
                    }
                    else if (item.FieldCode == "elegal_search_detail")
                    {
                        string legalUrl = AppSettingHelper.Configuration["LegalUrl"] ?? "";
                        string searchUrl = legalUrl + "?query=nikrxme4%2BjWxl0lwoOt%2FXsEFgcboRHv1xgXI56iwTvPDuGh6UJdUjrBBkjjnInbUqZKThJPGqHoYh6T2EglH2A%3D%3D";
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_zh",
                            column_value = "elegal.wistron.com"
                        });
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_en",
                            column_value = "elegal.wistron.com"
                        });
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_link",
                            column_value = searchUrl
                        });

                        continue;
                    }

                    //取得機密等級
                    if (item.FieldCode == "confiden_level")
                        confiden_level = PaperTrackingService.GetRowValue(row, item.FieldCode);

                    //取得機密等級(O單)
                    if (item.FieldCode == "case_apply_number")
                        confiden_level = PaperTrackingService.GetRowValue(row, "case_confiden_level"); 

                    //取得機密等級正本稽催
                    if (item.FieldCode == "achive_apply_number")
                        confiden_level = PaperTrackingService.GetRowValue(row, "achive_confiden_level");


                    mailContentDataList.Add(new MailContentDataResultModel()
                    {
                        column_name = item.FieldCode + "_zh",
                        column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_zh")

                    });
                    mailContentDataList.Add(new MailContentDataResultModel()
                    {
                        column_name = item.FieldCode + "_en",
                        column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_en")

                    });
                    if (item.HasLinkurl.HasValue && item.HasLinkurl.Value == 1)
                    {
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_link",
                            column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_link")

                        });
                    }

                }
            }
            #endregion

            if (userEmailContent.IsHiddenCode.HasValue && userEmailContent.IsHiddenCode == 1 && !string.IsNullOrEmpty(confiden_level) && confiden_level == "01")
                mailContentDataList = HiddenValueConvertHelper.ConvertToHiddenByList<MailContentDataResultModel>(mailContentDataList, "mail", "column_name", "column_value");

            foreach (var data in mailContentDataList)
            {
                //他方欄位特殊處理
                if (data.column_name == "other_party_zh") data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                if (data.column_name == "other_party_en") data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                if (data.column_name == "case_other_party_zh") data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                if (data.column_name == "case_other_party_en") data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                if (data.column_name == "achive_other_party_zh") data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                if (data.column_name == "achive_other_party_en") data.column_value = ConvertJsonToCommaSeparated(data.column_value);

                userEmailContent.MailSubject = userEmailContent.MailSubject.Replace($"{{{data.column_name}}}", data.column_value);
                userEmailContent.MailContent = userEmailContent.MailContent.Replace($"{{{data.column_name}}}", data.column_value);
            }

            if (userEmailContent.HavingTable == 1)
                SetMailTableContent(apply_number, userEmailContent, userEmailDictionaries);

            return userEmailContent;
        }


        /// <summary>
        /// 取得郵件模板列表
        /// </summary>
        /// <param name="applyNumber">单号</param>
        /// <param name="mailTypeList">模板类型</param>
        /// <param name="currentSigner">当前签核</param>
        /// <param name="currentStep">当前关卡</param>
        /// <returns></returns>
        public List<Interface.Api.Common.Model.SqlSugarModels.user_email_content> GetMailContentList(string applyNumber,
            List<MailTypeUtils> mailTypeList, string currentSigner, int currentStep)
        {
            var userEmailContentList = SqlSugarHelper.Db
                .Queryable<Interface.Api.Common.Model.SqlSugarModels.user_email_content>()
                .Where(x => mailTypeList.Select(o => o.ToString()).Contains(x.mail_type)).ToList();

            // 判断是否有模板
            if (!userEmailContentList.Any())
                return new List<Interface.Api.Common.Model.SqlSugarModels.user_email_content>();


            // 20250217 郵件開放欄位取值優化Pike
            // 由原来一份邮件取开放栏位 -> 多封一起取开放栏位
            var userEmailDictionaries = _flowRepository.GetUserEmailDictionaryList(
                userEmailContentList.Select(x => x.func_module).ToList(),
                mailTypeList.Select(o => o.ToString()).ToList());

            var UserEmailContentList = new List<Elegal.Interface.Api.Common.Model.SqlSugarModels.user_email_content>();

            foreach (var mailTypeItem in mailTypeList)
            {
                var userEmailContent = userEmailContentList.First(x => x.mail_type == mailTypeItem.toString());
                var mailContentDataList = new List<MailContentDataResultModel>();

                #region 處理需要根據單號查詢的字段

                //字段根据数据来源分组，同一个来源一次性查询，避免多次查询
                var group = userEmailDictionaries.Where(x =>
                        x.FieldType == userEmailContent.func_module && !string.IsNullOrEmpty(x.FieldSource))
                    .GroupBy(x => new { x.FieldSource, x.SourceType }).ToList();

                string legalUrl = AppSettingHelper.Configuration["LegalUrl"] ?? "";
                string confiden_level = string.Empty;

                foreach (var groupItem in group)
                {
                    // 20250312pike 郵件模板查詢，稽催類型郵件查詢特殊處理
                    var dt = _flowRepository.GetUserMailContentData(groupItem.Key.FieldSource, applyNumber,
                        currentSigner, currentStep, mailTypeItem.ToString());
                    // 郵件內容與標題的字段直接取第一筆數據 2025/1/15早會說的
                    var row = dt.GetRow(0);
                    // 沒查到一條數據，則直接跳過這個組
                    if (row == null) continue;

                    foreach (var item in groupItem)
                    {
                        switch (item.FieldCode)
                        {
                            case "elegal_code":
                                mailContentDataList.Add(new MailContentDataResultModel()
                                {
                                    column_name = item.FieldCode + "_zh",
                                    column_value = "elegal.wistron.com"
                                });
                                mailContentDataList.Add(new MailContentDataResultModel()
                                {
                                    column_name = item.FieldCode + "_en",
                                    column_value = "elegal.wistron.com"
                                });
                                mailContentDataList.Add(new MailContentDataResultModel()
                                {
                                    column_name = item.FieldCode + "_link",
                                    column_value = legalUrl
                                });
                                continue;
                            case "elegal_search_detail":
                                string searchUrl = legalUrl +
                                                   "?query=nikrxme4%2BjWxl0lwoOt%2FXsEFgcboRHv1xgXI56iwTvPDuGh6UJdUjrBBkjjnInbUqZKThJPGqHoYh6T2EglH2A%3D%3D";
                                mailContentDataList.Add(new MailContentDataResultModel()
                                {
                                    column_name = item.FieldCode + "_zh",
                                    column_value = "elegal.wistron.com"
                                });
                                mailContentDataList.Add(new MailContentDataResultModel()
                                {
                                    column_name = item.FieldCode + "_en",
                                    column_value = "elegal.wistron.com"
                                });
                                mailContentDataList.Add(new MailContentDataResultModel()
                                {
                                    column_name = item.FieldCode + "_link",
                                    column_value = searchUrl
                                });
                                continue;
                            // 取得機密等級
                            case "confiden_level":
                                confiden_level = PaperTrackingService.GetRowValue(row, item.FieldCode);
                                break;
                            // 取得機密等級(O單)
                            case "case_apply_number":
                                confiden_level = PaperTrackingService.GetRowValue(row, "case_confiden_level");
                                break;
                            // 取得機密等級正本稽催
                            case "achive_apply_number":
                                confiden_level = PaperTrackingService.GetRowValue(row, "achive_confiden_level");
                                break;
                        }

                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_zh",
                            column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_zh")
                        });
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_en",
                            column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_en")
                        });
                        if (item.HasLinkurl.HasValue && item.HasLinkurl.Value == 1)
                        {
                            mailContentDataList.Add(new MailContentDataResultModel()
                            {
                                column_name = item.FieldCode + "_link",
                                column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_link")
                            });
                        }
                    }
                }

                #endregion


                if (userEmailContent.is_hidden_code && !string.IsNullOrEmpty(confiden_level) && confiden_level == "01")
                    mailContentDataList =
                        HiddenValueConvertHelper.ConvertToHiddenByList<MailContentDataResultModel>(mailContentDataList,
                            "mail", "column_name", "column_value");

                foreach (var data in mailContentDataList)
                {
                    //他方欄位特殊處理
                    if (data.column_name == "other_party_zh")
                        data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                    if (data.column_name == "other_party_en")
                        data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                    if (data.column_name == "case_other_party_zh")
                        data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                    if (data.column_name == "case_other_party_en")
                        data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                    if (data.column_name == "achive_other_party_zh")
                        data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                    if (data.column_name == "achive_other_party_en")
                        data.column_value = ConvertJsonToCommaSeparated(data.column_value);

                    userEmailContent.mail_subject =
                        userEmailContent.mail_subject.Replace($"{{{data.column_name}}}", data.column_value);
                    userEmailContent.mail_content =
                        userEmailContent.mail_content.Replace($"{{{data.column_name}}}", data.column_value);
                }

                if (userEmailContent.having_table)
                    SetMailTableContentV2(applyNumber, userEmailContent, userEmailDictionaries);

                UserEmailContentList.Add(userEmailContent);
            }

            return UserEmailContentList;
        }

        #endregion

        /// <summary>
        /// 紙本郵件
        /// </summary>
        public static bool SendLendingMail(string lend_number, List<MailTypeUtils> mailTypeList)
        {
            try
            {
                if (mailTypeList == null || mailTypeList.Count == 0) return false;
                foreach (MailTypeUtils mail_type in mailTypeList)
                {
                    UserEmailContentDataRepository emailContentRepository = new UserEmailContentDataRepository();
                    List<UserEmailContent> userEmailContentList = emailContentRepository.Query(new UserEmailContentQueryCondition()
                    {
                        MailType = mail_type.ToString()
                    });
                    if (userEmailContentList == null || userEmailContentList.Count == 0) continue;

                    List<MailRecipientResultModel> recieverList = GetLendingReceiver(lend_number, userEmailContentList.FirstOrDefault().MailReType);

                    UserEmailContent sysEmailContent = GetMailContent(lend_number, mail_type, string.Join(",", recieverList.Select(x => x.recipient_emplid).ToList()), 1);
                    if (sysEmailContent == null) return false;

                    SysEmail se = new SysEmail();
                    #region 默認參數
                    //郵件發送人員
                    se.EReceiver = GetMailAddress(recieverList, sysEmailContent.IsReAgent.Value);
                    //拼接郵件抄送人員
                    se.ECc = GetMailAddress(GetLendingReceiver(lend_number, sysEmailContent.MailCcType), sysEmailContent.IsReAgent.Value);
                    //郵件標題
                    se.ESubject = sysEmailContent.MailSubject;
                    //郵件內容
                    se.EContent = sysEmailContent.MailContent;
                    se.ESendtime = DateTime.UtcNow;
                    se.ESendnum = 0;
                    se.EIssend = (int)YesOrNoUtils.No;
                    se.EType = sysEmailContent.MailType;
                    #endregion

                    //發送郵件
                    if (!string.IsNullOrEmpty(se.EReceiver) || !string.IsNullOrEmpty(se.ECc)) SysEmailDataService.Create(se);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 寄信
        /// </summary>
        public static bool SendTransferMail(string transfer_pic_number, List<MailTypeUtils> mailTypeList)
        {
            try
            {
                if (mailTypeList == null || mailTypeList.Count == 0) return false;
                foreach (MailTypeUtils mail_type in mailTypeList)
                {
                    UserEmailContentDataRepository emailContentRepository = new UserEmailContentDataRepository();
                    List<UserEmailContent> userEmailContentList = emailContentRepository.Query(new UserEmailContentQueryCondition()
                    {
                        MailType = mail_type.ToString()
                    });
                    if (userEmailContentList == null || userEmailContentList.Count == 0) continue;

                    List<MailRecipientResultModel> recieverList = GetTransferReceiver(transfer_pic_number, userEmailContentList.FirstOrDefault().MailReType);

                    UserEmailContent sysEmailContent = GetMailContent(transfer_pic_number, mail_type, MvcContext.UserInfo.current_emp, 1);
                    if (sysEmailContent == null) return false;

                    SysEmail se = new SysEmail();
                    #region 默認參數
                    //郵件發送人員
                    se.EReceiver = GetMailAddress(recieverList, sysEmailContent.IsReAgent.Value);
                    //拼接郵件抄送人員
                    se.ECc = GetMailAddress(GetTransferReceiver(transfer_pic_number, sysEmailContent.MailCcType), sysEmailContent.IsReAgent.Value);
                    //郵件標題
                    se.ESubject = sysEmailContent.MailSubject;
                    //郵件內容
                    se.EContent = sysEmailContent.MailContent;
                    se.ESendtime = DateTime.UtcNow;
                    se.ESendnum = 0;
                    se.EIssend = (int)YesOrNoUtils.No;
                    se.EType = sysEmailContent.MailType;
                    #endregion

                    //發送郵件
                    if (!string.IsNullOrEmpty(se.EReceiver) || !string.IsNullOrEmpty(se.ECc)) SysEmailDataService.Create(se);
                }

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 设定邮件表格内容
        /// </summary>
        /// <param name="list"></param>
        /// <param name="sysEmail"></param>
        private static void SetMailTableContent(string apply_number, UserEmailContent userEmailContent, List<UserEmailDictionary> userEmailDictionaries)
        {
            var tableJsons = JsonConvert.DeserializeObject<List<TableJson>>(userEmailContent.TableJson ?? "") ?? new List<TableJson>();

            foreach (var table in tableJsons)
            {
                if (userEmailContent.MailContent.Contains(table.tableId))
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.AppendLine("<table border=\"1\" style=\"border-collapse: collapse;border: 1px solid #ccc;text-align: left;font-size: 12px;\">");
                    stringBuilder.AppendLine("  <tbody>");
                    stringBuilder.AppendLine("      <tr>");
                    foreach (var col in table.columns)
                    {
                        var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                        var title = table.lang.Equals("ZH-TW") ? field.FieldCname : field.FieldEname;
                        stringBuilder.AppendLine($"          <td width=\"{(col.Equals("{other}") ? "400px" : "200px")}\">{title}</td>");
                    }
                    stringBuilder.AppendLine("      </tr>");

                    List<dynamic> list;
                    if ("02".Equals(userEmailContent.FuncModule))
                    {
                        switch (userEmailContent.MailType)
                        {
                            case "EL":
                                list = _repository.F_GetConfigEmpDimissionRemindByMail(apply_number);
                                break;
                            case "LO":
                                list = _repository.F_GetLendPaperRemindByMail(apply_number, "LO");
                                break;
                            case "LR":
                                list = _repository.F_GetLendPaperRemindByMail(apply_number, "LR");
                                break;
                            case "OS":
                                list = _repository.F_GetApplicationRemindByMail(apply_number, "OS");
                                break;
                            case "CS":
                                list = _repository.F_GetApplicationRemindByMail(apply_number, "CS");
                                break;
                            case "ON":
                                list = _repository.F_GetApplicationRemindByMail(apply_number, "ON");
                                break;
                            case "CE":
                                list = _repository.F_GetApplicationRemindByMail(apply_number, "CE");
                                break;
                            case "PLC":
                                list = _repository.F_GetApplicationRemindByMail(apply_number, "PLC");
                                break;
                            case "OBO":
                                list = _repository.F_GetApplicationRemindByMail(apply_number, "OBO");
                                break;
                            case "DM":
                                list = _repository.F_GetDownloadOverRemindDetail();
                                break;
                            default:
                                list = new();
                                break;
                        }
                    }
                    else
                    {
                        if (apply_number.StartsWith("L"))
                            list = _repository.GetFGetPaperLendingByMail(apply_number, "");
                        else if (apply_number.StartsWith("TL"))
                            list = _repository.GetFGetLegalTransferByMail(apply_number);
                        else if (apply_number.StartsWith("T"))
                            list = _repository.GetFGetTransferByMail(apply_number);
                        else
                            list = _repository.GetFGetApplicationByMail(apply_number, "");
                    }

                    #region 表格內容隱碼數據處理
                    List<dynamic> hidden_list = new List<dynamic>();
                    //循環遍歷其中是否包含隱碼欄位，如有則需要單筆數據進行隱碼處理
                    foreach (var hitem in list) 
                    {
                        var hItemDict = (IDictionary<string, object>)hitem;
                        if (hItemDict.TryGetValue("confiden_level", out var confidenLevel))
                        {
                            if (confidenLevel != null && confidenLevel.ToString() == "01")
                            {
                                hidden_list.Add(HiddenValueConvertHelper.ConvertToHiddenSingleByDynamic(hitem, "mail"));
                            }
                            else
                            {
                                hidden_list.Add(hitem);
                            }
                        }
                    }
                    //如果隱碼規則無適用數據，則進行原始數據賦值
                    if (!hidden_list.Any()) hidden_list = list;
                    #endregion

                    foreach (var item in hidden_list)
                    {
                        if (item == null)
                            continue;

                        stringBuilder.AppendLine("      <tr>");
                        StringBuilder tbBuilder = new StringBuilder();
                        foreach (var col in table.columns)
                        {
                            var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                            var title = $"{{{field.FieldCode}_{(table.lang.Equals("ZH-TW") ? "zh" : "en")}}}";
                            tbBuilder.AppendLine($"          <td>{title}</td>");
                        }

                        var itemDict = (IDictionary<string, object>)item;
                        foreach (var col in table.columns)
                        {
                            var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                            if (field != null)
                            {
                                string langType = table.lang.Equals("ZH-TW") ? "_zh" : "_en";
                                var zhKey = $"{{{field.FieldCode + langType}}}";
                                var value = itemDict.ContainsKey(field.FieldCode + langType) ? itemDict[field.FieldCode + langType]?.ToString() ?? string.Empty : string.Empty;
                                //他方欄位特殊處理
                                if (zhKey == "{other_party_zh}" || zhKey == "{other_party_en}") value = ConvertJsonToCommaSeparated(value);
                                //表格申請單鏈接欄位處理 20250224 pike
                                if (field.HasLinkurl == 1)
                                {
                                    ApplicationUrl application = ApplicationUrlDataService.FindByKey(value);
                                    string link = string.Empty;
                                    if (application != null)
                                    {
                                        link = application.HyperlinkPath;
                                        tbBuilder.Replace(zhKey, @$"<a title=""點擊此處跳轉"" href=""{link}"" style="""">{zhKey}</a>");
                                    }
                                }

                                tbBuilder = tbBuilder.Replace(zhKey, value);
                            }
                        }

                        stringBuilder.Append(tbBuilder);
                        stringBuilder.AppendLine("      </tr>");
                    }
                    stringBuilder.AppendLine("  </tbody>");
                    stringBuilder.AppendLine("</table>");

                    userEmailContent.MailContent = userEmailContent.MailContent.Replace($"{{{table.tableId}}}", stringBuilder.ToString());
                }
            }
        }

        /// <summary>
        /// 设定邮件表格内容
        /// </summary>
        /// <param name="applyNumber">单号</param>
        /// <param name="userEmailContent"></param>
        /// <param name="userEmailDictionaries"></param>
        private void SetMailTableContentV2(string applyNumber, Elegal.Interface.Api.Common.Model.SqlSugarModels.user_email_content userEmailContent,
            List<UserEmailDictionary> userEmailDictionaries)
        {

            var tableJsons = JsonConvert.DeserializeObject<List<TableJson>>(userEmailContent.table_json ?? "") ??
                             new List<TableJson>();

            foreach (var table in tableJsons)
            {
                if (userEmailContent.mail_content.Contains(table.tableId))
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.AppendLine(
                        "<table border=\"1\" style=\"border-collapse: collapse;border: 1px solid #ccc;text-align: left;font-size: 12px;\">");
                    stringBuilder.AppendLine("  <tbody>");
                    stringBuilder.AppendLine("      <tr>");
                    foreach (var col in table.columns)
                    {
                        var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                        var title = table.lang.Equals("ZH-TW") ? field.FieldCname : field.FieldEname;
                        stringBuilder.AppendLine(
                            $"          <td width=\"{(col.Equals("{other}") ? "400px" : "200px")}\">{title}</td>");
                    }

                    stringBuilder.AppendLine("      </tr>");

                    List<dynamic> list;
                    if ("02".Equals(userEmailContent.func_module))
                    {
                        switch (userEmailContent.mail_type)
                        {
                            case "EL":
                                list = _repository.F_GetConfigEmpDimissionRemindByMail(applyNumber);
                                break;
                            case "LO":
                                list = _repository.F_GetLendPaperRemindByMail(applyNumber, "LO");
                                break;
                            case "LR":
                                list = _repository.F_GetLendPaperRemindByMail(applyNumber, "LR");
                                break;
                            case "OS":
                                list = _repository.F_GetApplicationRemindByMail(applyNumber, "OS");
                                break;
                            case "CS":
                                list = _repository.F_GetApplicationRemindByMail(applyNumber, "CS");
                                break;
                            case "ON":
                                list = _repository.F_GetApplicationRemindByMail(applyNumber, "ON");
                                break;
                            case "CE":
                                list = _repository.F_GetApplicationRemindByMail(applyNumber, "CE");
                                break;
                            case "PLC":
                                list = _repository.F_GetApplicationRemindByMail(applyNumber, "PLC");
                                break;
                            case "OBO":
                                list = _repository.F_GetApplicationRemindByMail(applyNumber, "OBO");
                                break;
                            case "DM":
                                list = _repository.F_GetDownloadOverRemindDetail();
                                break;
                            default:
                                list = new();
                                break;
                        }
                    }
                    else
                    {
                        if (applyNumber.StartsWith("L"))
                            list = _repository.GetFGetPaperLendingByMail(applyNumber, "");
                        else if (applyNumber.StartsWith("TL"))
                            list = _repository.GetFGetLegalTransferByMail(applyNumber);
                        else if (applyNumber.StartsWith("T"))
                            list = _repository.GetFGetTransferByMail(applyNumber);
                        else
                            list = _repository.GetFGetApplicationByMail(applyNumber, "");
                    }

                    #region 表格內容隱碼數據處理

                    List<dynamic> hidden_list = new List<dynamic>();
                    //循環遍歷其中是否包含隱碼欄位，如有則需要單筆數據進行隱碼處理
                    foreach (var hitem in list)
                    {
                        var hItemDict = (IDictionary<string, object>)hitem;
                        if (hItemDict.TryGetValue("confiden_level", out var confidenLevel))
                        {
                            if (confidenLevel != null && confidenLevel.ToString() == "01")
                            {
                                hidden_list.Add(HiddenValueConvertHelper.ConvertToHiddenSingleByDynamic(hitem, "mail"));
                            }
                            else
                            {
                                hidden_list.Add(hitem);
                            }
                        }
                    }

                    //如果隱碼規則無適用數據，則進行原始數據賦值
                    if (!hidden_list.Any()) hidden_list = list;

                    #endregion

                    foreach (var item in hidden_list)
                    {
                        if (item == null)
                            continue;

                        stringBuilder.AppendLine("      <tr>");
                        StringBuilder tbBuilder = new StringBuilder();
                        foreach (var col in table.columns)
                        {
                            var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                            var title = $"{{{field.FieldCode}_{(table.lang.Equals("ZH-TW") ? "zh" : "en")}}}";
                            tbBuilder.AppendLine($"          <td>{title}</td>");
                        }

                        var itemDict = (IDictionary<string, object>)item;
                        foreach (var col in table.columns)
                        {
                            var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                            if (field != null)
                            {
                                string langType = table.lang.Equals("ZH-TW") ? "_zh" : "_en";
                                var zhKey = $"{{{field.FieldCode + langType}}}";
                                var value = itemDict.ContainsKey(field.FieldCode + langType)
                                    ? itemDict[field.FieldCode + langType]?.ToString() ?? string.Empty
                                    : string.Empty;
                                //他方欄位特殊處理
                                if (zhKey == "{other_party_zh}" || zhKey == "{other_party_en}")
                                    value = ConvertJsonToCommaSeparated(value);
                                //表格申請單鏈接欄位處理 20250224 pike
                                if (field.HasLinkurl == 1)
                                {
                                    ApplicationUrl application = ApplicationUrlDataService.FindByKey(value);
                                    string link = string.Empty;
                                    if (application != null)
                                    {
                                        link = application.HyperlinkPath;
                                        tbBuilder.Replace(zhKey,
                                            @$"<a title=""點擊此處跳轉"" href=""{link}"" style="""">{zhKey}</a>");
                                    }
                                }

                                tbBuilder = tbBuilder.Replace(zhKey, value);
                            }
                        }

                        stringBuilder.Append(tbBuilder);
                        stringBuilder.AppendLine("      </tr>");
                    }

                    stringBuilder.AppendLine("  </tbody>");
                    stringBuilder.AppendLine("</table>");

                    userEmailContent.mail_content =
                        userEmailContent.mail_content.Replace($"{{{table.tableId}}}", stringBuilder.ToString());
                }
            }
        }


        public static List<MailRecipientResultModel> GetLendingReceiver(string lend_number, string receiver_type)
        {
            //根據 lendid 獲取 借出單號，申請日期，其他申請單號，經辦人(單一數據)
            paper_lending_application application = _repository.GetPaperLendingApplication(lend_number);
            if (application == null) return new List<MailRecipientResultModel>();

            paper_lending_detail details = _repository.GetPaperLendingDetail(application.lend_id).FirstOrDefault();

            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);

            // 用來儲存收件人的清單
            List<MailRecipientResultModel> recipients = new List<MailRecipientResultModel>();

            // 遍歷 receiverList，根據 Key 判斷收件人
            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {
                    //角色
                    case "02":
                        PUserRoleDataRepository pUserRoleDataRepository = new PUserRoleDataRepository();
                        List<PUserRole> pUserRoles = pUserRoleDataRepository.Query(new PUserRoleQueryCondition()
                        {
                            SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = receiver.Value, Field = "r_id" }] }
                        });
                        if (pUserRoles != null && pUserRoles.Count > 0)
                        {
                            foreach (PUserRole role in pUserRoles)
                            {
                                if (!string.IsNullOrEmpty(role.UId))
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = role.UId, recipient_deptid = GetEmpl(role.UId).deptid });
                            }
                        }
                        break;
                    //總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalEMail, recipient_deptid = legalEMail });
                        break;
                    //必要揭露人員名單-內部同仁(僅適用紙本借出)
                    case "15":
                        paper_lending_demand demand = _repository.GetPaperLendingDemand(application.lend_id);
                        // 如有其他申請單，需撈取必要揭露人員名單-內部同仁
                        if (demand != null && !string.IsNullOrEmpty(demand.retrieve_number))
                        {
                            List<other_application_expose_person> exposeList = _otherApplyRepository.GetOtherApplicationExposePerson(demand.retrieve_number);
                            if (exposeList != null && exposeList.Count > 0)
                            {
                                List<other_application_expose_person> innerList = exposeList.Where(x => x.expose_type == "01").ToList();
                                if (innerList != null && innerList.Count > 0)
                                {
                                    foreach (other_application_expose_person inner in innerList)
                                    {
                                        if (inner != null && !string.IsNullOrEmpty(inner.expose_emplid))
                                        {
                                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = inner.expose_emplid, recipient_deptid = GetEmpl(inner.expose_emplid).deptid });
                                        }
                                    }
                                }
                            }
                        }

                        // 如果沒有expose_emplid，則加上lend_handler_emplid
                        if (!recipients.Any(r => r.recipient_type == receiver.Key))
                        {
                            if (!string.IsNullOrEmpty(application.lend_handler_emplid))
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = application.lend_handler_emplid, recipient_deptid = application.lend_handler_deptid });
                            }
                            // 如果沒有lend_handler_emplid，則加上legalEMail
                            else
                            {
                                legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                                recipients.Add(new MailRecipientResultModel() { recipient_type = "08", recipient_emplid = legalEMail, recipient_deptid = legalEMail });
                            }
                        }
                        break;
                    //申請人
                    case "04":
                        string fill = application.lend_fill_emplid ?? "";
                        if (!string.IsNullOrEmpty(fill)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill, recipient_deptid = application.lend_fill_deptid });
                        break;
                    //申請人代理人
                    case "17":
                        string fillAgent = _applyPermissionRepository.GetAgenByEmp(application.lend_fill_emplid, application.lend_fill_deptid);
                        if (!string.IsNullOrEmpty(fillAgent))
                        {
                            var agentEmployee = GetEmpl(fillAgent);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        break;
                    //自動化設定人員(For Job)
                    case "25":
                        SysAutoJob job = SysAutoJobDataService.Find(new Interface.ApiData.Service.Model.Condition.SysAutoJobCondition()
                        {
                            JobRunProgram = receiver.Value.FirstOrDefault()
                        });
                        if (job != null && !string.IsNullOrEmpty(job.JobMailAddress))
                        {
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = job.JobMailAddress, recipient_deptid = job.JobMailAddress });
                        }
                        break;
                    //Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = adminEMail, recipient_deptid = adminEMail });
                        break;
                    //經辦人
                    case "05":
                        string pic = application.lend_handler_emplid ?? "";
                        if (!string.IsNullOrEmpty(pic)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic, recipient_deptid = application.lend_handler_deptid });
                        break;
                    default:
                        break;
                }
            }

            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }

        public static List<MailRecipientResultModel> GetApplicationJobReceiver(string apply_number, string receiver_type, string mail_type = "")
        {
            V_GetAllApplication application = _flowRepository.GetAllApplication(apply_number);
            if (application == null && !mail_type.Equals("PLC")) return new List<MailRecipientResultModel>();

            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);

            // 用來儲存收件人的清單
            List<MailRecipientResultModel> recipients = new List<MailRecipientResultModel>();

            // 遍歷 receiverList，根據 Key 判斷收件人
            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {
                    //關卡人員
                    case "01":
                        List<ApplicationApproveProcess> stepList = _processRepository.GetApplicationApprove(application.apply_type, application.form_type, application.apply_number);
                        foreach (ApplicationApproveProcess step in stepList)
                        {
                            if (!string.IsNullOrEmpty(step.signerEmplid) && receiver.Value.Any(v => v.ToLower().Contains(step.stepName.ToLower())))
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = step.signerEmplid, recipient_deptid = step.signerDeptid });
                            }
                        }
                        break;
                    //角色
                    case "02":
                        PUserRoleDataRepository pUserRoleDataRepository = new PUserRoleDataRepository();
                        List<PUserRole> pUserRoles = pUserRoleDataRepository.Query(new PUserRoleQueryCondition()
                        {
                            SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = receiver.Value, Field = "r_id" }] }
                        });
                        if (pUserRoles != null && pUserRoles.Count > 0)
                        {
                            foreach (PUserRole role in pUserRoles)
                            {
                                if (!string.IsNullOrEmpty(role.UId))
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = role.UId, recipient_deptid = GetEmpl(role.UId).deptid });
                            }
                        }
                        break;
                    //當關應簽核人員
                    //20250320查詢當關應簽核人員邏輯修改
                    case "03":
                        List<flow_step_signer> signerList = _flowRepository.GetFlowStepSignerAndAgent(application.apply_number);
                        if (signerList != null && signerList.Count > 0)
                        {
                            foreach (flow_step_signer signer in signerList)
                            {
                                if (!string.IsNullOrEmpty(signer.signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = signer.signer_emplid, recipient_deptid = signer.signer_deptid });
                                }
                            }
                        }
                        break;
                    //填單人
                    case "04":
                        string fill = application.fill_emplid ?? "";
                        if (!string.IsNullOrEmpty(fill)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill, recipient_deptid = application.fill_deptid });
                        break;
                    //經辦人
                    case "05":
                        string pic = application.pic_emplid ?? "";
                        if (!string.IsNullOrEmpty(pic)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic, recipient_deptid = application.pic_deptid });
                        break;
                    //現任聯絡人
                    case "06":
                        string incumbentEmplid = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbentEmplid)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbentEmplid, recipient_deptid = application.incumbent_deptid });
                        break;
                    //承辦法務
                    case "07":
                        string legalAffairs = application.legal_affairs_emplid ?? "";
                        if (!string.IsNullOrEmpty(legalAffairs)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalAffairs, recipient_deptid = GetEmpl(legalAffairs).deptid });
                        break;
                    //總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalEMail, recipient_deptid = "" });
                        break;
                    //加簽/特殊加簽人員
                    case "10":
                        //20250320查詢加簽/特殊加簽人員邏輯修改
                        List<flow_step_signer_invitee> invitees = _flowRepository.GetFlowStepSignerAllInviteesAndAgent(application.apply_number);
                        if (invitees != null && invitees.Count > 0)
                        {
                            foreach (flow_step_signer_invitee invitee in invitees)
                            {
                                if (!string.IsNullOrEmpty(invitee.invitee_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = invitee.invitee_emplid, recipient_deptid = invitee.invitee_deptid });
                                }
                            }
                        }
                        break;
                    //已簽核過人員
                    case "11":
                        List<flow_step_history> historyList = _flowRepository.GetFlowStepHistory(application.apply_number);
                        if (historyList != null && historyList.Count > 0)
                        {
                            foreach (flow_step_history history in historyList)
                            {
                                if (!string.IsNullOrEmpty(history.actual_signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = history.actual_signer_emplid, recipient_deptid = history.actual_signer_deptid });
                                }
                            }
                        }
                        break;
                    //當關簽核人員
                    case "12":
                        List<flow_step_signer> signers = _flowRepository.GetFlowStepSigner(application.apply_number);
                        if (signers != null && signers.Count > 0)
                        {
                            foreach (flow_step_signer signer in signers)
                            {
                                if (!string.IsNullOrEmpty(signer.signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = signer.signer_emplid, recipient_deptid = signer.signer_deptid });
                                }
                            }
                        }
                        break;
                    //經辦人代理人
                    case "13":
                        string picAgent = _applyPermissionRepository.GetAgenByEmp(application.pic_emplid, application.pic_deptid);
                        if (!string.IsNullOrEmpty(picAgent))
                        {
                            var agentEmployee = GetEmpl(picAgent);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        break;
                    //合約管理人(僅適用合約申請)
                    case "14":
                        List<string> applyList = new List<string>();
                        //合約申請
                        if (application.apply_type == "C" || application.form_type == "R" || application.form_type == "AR")
                        {
                            applyList.Add(apply_number);
                        }
                        //其他申請C/D類
                        else if (application.apply_type == "O")
                        {
                            other_application other = _otherApplyRepository.GetOtherApplication(apply_number);
                            if (other.form_type == "C")
                            {
                                List<other_application_c> otherCList = _otherApplyRepository.GetOtherApplicationC(apply_number);
                                if (otherCList != null && otherCList.Count > 0)
                                {
                                    foreach (other_application_c otherC in otherCList)
                                    {
                                        if (!string.IsNullOrEmpty(otherC.voided_apply_number)) applyList.Add(otherC.voided_apply_number);
                                    }
                                }
                            }
                            else if (other.form_type == "D")
                            {
                                List<other_application_d> otherDList = _otherApplyRepository.GetOtherApplicationD(apply_number);
                                if (otherDList != null && otherDList.Count > 0)
                                {
                                    foreach (other_application_d otherD in otherDList)
                                    {
                                        if (!string.IsNullOrEmpty(otherD.seal_apply_number)) applyList.Add(otherD.seal_apply_number);
                                    }
                                }
                            }
                        }
                        foreach (string apply in applyList)
                        {
                            V_GetAllApplication form = _flowRepository.GetAllApplication(apply);
                            if (form == null) continue;
                            int flow_step = Convert.ToInt16(form.form_type);
                            if (application.form_type == "R") flow_step = 4;
                            if (application.form_type == "AR") flow_step = 12;
                            //原簽核流程已刪除，取得最新的合約管理人
                            List<ApplicationApproveProcess> contractMaTaskList = FlowStepProcessService.GetOtherApplicationApproveProcessSignerSP(form.apply_type, form.form_type, apply, _flowRepository.GetContractMaTaskStepid(flow_step));
                            if (contractMaTaskList != null && contractMaTaskList.Count > 0)
                            {
                                foreach (ApplicationApproveProcess contractMaTask in contractMaTaskList)
                                {
                                    if (contractMaTask != null && !string.IsNullOrEmpty(contractMaTask.signerEmplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = contractMaTask.signerEmplid, recipient_deptid = contractMaTask.signerDeptid });
                                    }
                                }
                            }
                        }
                        break;
                    //必要揭露人員名單-內部同仁(僅適用其他申請)
                    case "15":
                        List<other_application_expose_person> exposeList = _otherApplyRepository.GetOtherApplicationExposePerson(apply_number);
                        if (exposeList == null || exposeList.Count == 0) continue;

                        List<other_application_expose_person> innerList = exposeList.Where(x => x.expose_type == "01").ToList();
                        if (innerList == null || innerList.Count == 0) continue;

                        foreach (other_application_expose_person inner in innerList)
                        {
                            if (inner != null && !string.IsNullOrEmpty(inner.expose_emplid))
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = inner.expose_emplid, recipient_deptid = GetEmpl(inner.expose_emplid).deptid });
                            }
                        }
                        break;
                    //印鑑保管人(只有合約申請有)
                    case "16":
                        form_application formApply = _formApplyRepository.GetFormApplyInfo(apply_number);
                        if (formApply == null) continue;

                        if (!string.IsNullOrEmpty(formApply.seal_type) && formApply.seal_type == "1" && !string.IsNullOrEmpty(formApply.other_seal_type))
                        {
                            List<SealCustodianManagement> sealList = SealCustodianManagementDataService.Query(new SealCustodianManagementQueryCondition()
                            {
                                SealType = formApply.other_seal_type,
                                EntityId = formApply.entity_id
                            });

                            if (sealList == null || sealList.Count == 0) continue;
                            foreach (SealCustodianManagement seal in sealList)
                            {
                                if (!string.IsNullOrEmpty(seal.EmpId))
                                {
                                    var sealEmp = GetEmpl(seal.EmpId);
                                    if (sealEmp != null)
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = sealEmp.emplid, recipient_deptid = sealEmp.deptid });
                                    }
                                }
                            }
                        }

                        break;
                    //申請人代理人
                    case "17":
                        string fillAgent = _applyPermissionRepository.GetAgenByEmp(application.fill_emplid, application.fill_deptid);
                        if (!string.IsNullOrEmpty(fillAgent))
                        {
                            var agentEmployee = GetEmpl(fillAgent);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        break;
                    //申請人/經辦人/現任聯絡人
                    case "20":
                        //現任聯絡人
                        string incumbent_emp = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp))
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbent_emp, recipient_deptid = application.incumbent_deptid });
                        else
                        {
                            //填單人
                            string fill_emp = application.fill_emplid ?? "";
                            if (!string.IsNullOrEmpty(fill_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill_emp, recipient_deptid = application.fill_deptid });

                            //經辦人
                            string pic_emp = application.pic_emplid ?? "";
                            if (!string.IsNullOrEmpty(pic_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic_emp, recipient_deptid = application.pic_deptid });
                        }
                        break;
                    //合約管理人及其代理人
                    case "22":
                        List<string> applys = new List<string>();
                        //合約申請
                        if (application.apply_type == "C" || application.form_type == "R" || application.form_type == "AR")
                        {
                            applys.Add(apply_number);
                        }
                        //其他申請C/D類
                        else if (application.apply_type == "O")
                        {
                            other_application other = _otherApplyRepository.GetOtherApplication(apply_number);
                            if (other.form_type == "C")
                            {
                                List<other_application_c> otherCList = _otherApplyRepository.GetOtherApplicationC(apply_number);
                                if (otherCList != null && otherCList.Count > 0)
                                {
                                    foreach (other_application_c otherC in otherCList)
                                    {
                                        if (!string.IsNullOrEmpty(otherC.voided_apply_number)) applys.Add(otherC.voided_apply_number);
                                    }
                                }
                            }
                            else if (other.form_type == "D")
                            {
                                List<other_application_d> otherDList = _otherApplyRepository.GetOtherApplicationD(apply_number);
                                if (otherDList != null && otherDList.Count > 0)
                                {
                                    foreach (other_application_d otherD in otherDList)
                                    {
                                        if (!string.IsNullOrEmpty(otherD.seal_apply_number)) applys.Add(otherD.seal_apply_number);
                                    }
                                }
                            }
                        }
                        foreach (string apply in applys)
                        {
                            V_GetAllApplication form = _flowRepository.GetAllApplication(apply);
                            if (form == null) continue;
                            int flow_step = Convert.ToInt16(form.form_type);
                            if (application.form_type == "R") flow_step = 4;
                            if (application.form_type == "AR") flow_step = 12;
                            //原簽核流程已刪除，取得最新的合約管理人
                            List<ApplicationApproveProcess> contractMaTaskList = FlowStepProcessService.GetOtherApplicationApproveProcessSignerSP(form.apply_type, form.form_type, apply, _flowRepository.GetContractMaTaskStepid(flow_step));
                            if (contractMaTaskList != null && contractMaTaskList.Count > 0)
                            {
                                foreach (ApplicationApproveProcess contractMaTask in contractMaTaskList)
                                {
                                    if (contractMaTask != null && !string.IsNullOrEmpty(contractMaTask.signerEmplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = contractMaTask.signerEmplid, recipient_deptid = contractMaTask.signerDeptid });
                                        string signerAgent = _applyPermissionRepository.GetAgenByEmp(contractMaTask.signerEmplid, contractMaTask.signerDeptid);
                                        if (!string.IsNullOrEmpty(signerAgent))
                                        {
                                            var agentEmployee = GetEmpl(signerAgent);
                                            if (agentEmployee != null)
                                            {
                                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    //申請人/現任聯絡人
                    case "23":
                        //現任聯絡人
                        string incumbent_emp_23 = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp_23))
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbent_emp_23, recipient_deptid = application.incumbent_deptid });
                        else
                        {
                            //填單人
                            string fill_emp = application.fill_emplid ?? "";
                            if (!string.IsNullOrEmpty(fill_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill_emp, recipient_deptid = application.fill_deptid });
                        }
                        break;
                    //經辦人/現任聯絡人
                    case "24":
                        //現任聯絡人
                        string incumbent_emp_24 = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp_24))
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbent_emp_24, recipient_deptid = application.incumbent_deptid });
                        else
                        {
                            //經辦人
                            string pic_emp = application.pic_emplid ?? "";
                            if (!string.IsNullOrEmpty(pic_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic_emp, recipient_deptid = application.pic_deptid });
                        }
                        break;
                    //Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = adminEMail, recipient_deptid = adminEMail });
                        break;
                    //經辦人部門正主管
                    case "27":
                        PsSubOgLglVwA psSubOgLgl = PsSubOgLglVwADataService.FindByKey(application.pic_deptid);
                        if (psSubOgLgl != null)
                        {
                            string pic_manger_emp = psSubOgLgl.ManagerId ?? "";
                            if (!string.IsNullOrEmpty(pic_manger_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = psSubOgLgl.ManagerId, recipient_deptid = application.pic_deptid });
                        }
                        break;
                    //經辦人部門主管
                    case "28":
                        PsSubOgLglVwA psSubOgLglVwA = PsSubOgLglVwADataService.FindByKey(application.pic_deptid);
                        if (psSubOgLglVwA != null)
                        {
                            string pic_manger_emp = psSubOgLglVwA.ManagerId ?? "";
                            if (!string.IsNullOrEmpty(pic_manger_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = psSubOgLglVwA.ManagerId, recipient_deptid = application.pic_deptid });
                        }
                        break;
                    //組織主管應簽核人部門現任正主管(排除緯穎)
                    case "29":
                        List<ps_sub_ee_lgl_vw_a> ee = _flowRepository.GetAllORGManager(apply_number);
                        ee.ForEach(e =>
                        {
                            if (e != null && !string.IsNullOrEmpty(e.emplid) && !string.IsNullOrEmpty(e.deptid))
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = e.emplid, recipient_deptid = e.deptid });
                        });
                        break;
                    default:
                        break;
                }
            }

            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }

        /// <summary>
        /// 取得收件者
        /// </summary>
        public static List<MailRecipientResultModel> GetReceiver(string apply_type, string apply_number, string receiver_type, List<ApplicationApproveProcess> currentStepList, List<MailRecipientResultModel> inputReceiverList)
        {
            V_GetAllApplication application = _flowRepository.GetAllApplication(apply_type, apply_number);
            if (application == null) return new List<MailRecipientResultModel>();

            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);

            // 用來儲存收件人的清單
            List<MailRecipientResultModel> recipients = new List<MailRecipientResultModel>();

            // 遍歷 receiverList，根據 Key 判斷收件人
            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {
                    //關卡人員
                    case "01":
                        List<ApplicationApproveProcess> stepList = _processRepository.GetApplicationApprove(application.apply_type, application.form_type, application.apply_number);
                        foreach (ApplicationApproveProcess step in stepList)
                        {
                            if (!string.IsNullOrEmpty(step.signerEmplid) && receiver.Value.Any(v => v.ToLower().Contains(step.stepName.ToLower())))
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = step.signerEmplid, recipient_deptid = step.signerDeptid });
                            }
                        }
                        break;
                    //角色
                    case "02":
                        PUserRoleDataRepository pUserRoleDataRepository = new PUserRoleDataRepository();
                        List<PUserRole> pUserRoles = pUserRoleDataRepository.Query(new PUserRoleQueryCondition()
                        {
                            SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = receiver.Value, Field = "r_id" }] }
                        });
                        if (pUserRoles != null && pUserRoles.Count > 0)
                        {
                            foreach (PUserRole role in pUserRoles)
                            {
                                if (!string.IsNullOrEmpty(role.UId))
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = role.UId, recipient_deptid = GetEmpl(role.UId).deptid });
                            }
                        }
                        break;
                    //當關應簽核人員及其代理人
                    case "03":
                        //20250219當傳入當關人員時取傳入值
                        if (inputReceiverList != null && inputReceiverList.Count > 0)
                        {
                            recipients.AddRange(inputReceiverList);
                            inputReceiverList.ForEach(e =>
                            {
                                string signerAgent = _applyPermissionRepository.GetAgenByEmp(e.recipient_emplid, e.recipient_deptid);
                                if (!string.IsNullOrEmpty(signerAgent))
                                {
                                    var agentEmployee = GetEmpl(signerAgent);
                                    if (agentEmployee != null)
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                                    }
                                }
                            });

                        }
                        else
                        {
                            List<flow_step_signer> signerList = _flowRepository.GetFlowStepSigner(application.apply_number);
                            if (signerList != null && signerList.Count > 0)
                            {
                                foreach (flow_step_signer signer in signerList)
                                {
                                    if (!string.IsNullOrEmpty(signer.signer_emplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = signer.signer_emplid, recipient_deptid = signer.signer_deptid });
                                        string signerAgent = _applyPermissionRepository.GetAgenByEmp(signer.signer_emplid, signer.signer_deptid);
                                        if (!string.IsNullOrEmpty(signerAgent))
                                        {
                                            var agentEmployee = GetEmpl(signerAgent);
                                            if (agentEmployee != null)
                                            {
                                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                                            }
                                        }
                                    }
                                }
                            }
                        }

                        break;
                    //填單人
                    case "04":
                        string fill = application.fill_emplid ?? "";
                        if (!string.IsNullOrEmpty(fill)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill, recipient_deptid = application.fill_deptid });
                        break;
                    //經辦人
                    case "05":
                        string pic = application.pic_emplid ?? "";
                        if (!string.IsNullOrEmpty(pic)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic, recipient_deptid = application.pic_deptid });
                        break;
                    //現任聯絡人
                    case "06":
                        string incumbentEmplid = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbentEmplid)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbentEmplid, recipient_deptid = application.incumbent_deptid });
                        break;
                    //承辦法務
                    case "07":
                        string legalAffairs = application.legal_affairs_emplid ?? "";
                        if (!string.IsNullOrEmpty(legalAffairs)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalAffairs, recipient_deptid = GetEmpl(legalAffairs).deptid });
                        break;
                    //總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalEMail, recipient_deptid = "" });
                        break;
                    //被移除的加簽/特殊加簽人員
                    case "09":
                        if (inputReceiverList != null && inputReceiverList.Count > 0)
                            recipients.AddRange(inputReceiverList);
                        break;
                    //加簽/特殊加簽人員
                    case "10":
                        if (inputReceiverList != null && inputReceiverList.Count > 0)
                        {
                            recipients.AddRange(inputReceiverList);
                        }
                        else
                        {
                            List<flow_step_signer_invitee> invitees = _flowRepository.GetFlowStepSignerInvitees(application.apply_number);
                            if (invitees != null && invitees.Count > 0)
                            {
                                foreach (flow_step_signer_invitee invitee in invitees)
                                {
                                    if (!string.IsNullOrEmpty(invitee.invitee_emplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = invitee.invitee_emplid, recipient_deptid = invitee.invitee_deptid });
                                    }
                                }
                            }
                        }
                        break;
                    //已簽核過人員
                    case "11":
                        List<flow_step_history> historyList = _flowRepository.GetFlowStepHistory(application.apply_number);
                        if (historyList != null && historyList.Count > 0)
                        {
                            foreach (flow_step_history history in historyList)
                            {
                                if (!string.IsNullOrEmpty(history.actual_signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = history.actual_signer_emplid, recipient_deptid = history.actual_signer_deptid });
                                }
                            }
                        }
                        break;
                    //當關簽核人員
                    case "12":
                        List<flow_step_signer> signers = _flowRepository.GetFlowStepSigner(application.apply_number);
                        if (signers != null && signers.Count > 0)
                        {
                            foreach (flow_step_signer signer in signers)
                            {
                                if (!string.IsNullOrEmpty(signer.signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = signer.signer_emplid, recipient_deptid = signer.signer_deptid });
                                }
                            }
                        }
                        break;
                    //經辦人代理人
                    case "13":
                        string picAgent = _applyPermissionRepository.GetAgenByEmp(application.pic_emplid, application.pic_deptid);
                        if (!string.IsNullOrEmpty(picAgent))
                        {
                            var agentEmployee = GetEmpl(picAgent);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        break;
                    //合約管理人(僅適用合約申請+建檔)
                    case "14":
                        List<string> applyList = new List<string>();
                        //合約申請+建檔
                        if (apply_type == "C" || apply_type == "A")
                        {
                            applyList.Add(apply_number);
                        }
                        //其他申請C/D類
                        else if (apply_type == "O")
                        {
                            other_application other = _otherApplyRepository.GetOtherApplication(apply_number);
                            if (other.form_type == "C")
                            {
                                List<other_application_c> otherCList = _otherApplyRepository.GetOtherApplicationC(apply_number);
                                if (otherCList != null && otherCList.Count > 0)
                                {
                                    foreach (other_application_c otherC in otherCList)
                                    {
                                        if (!string.IsNullOrEmpty(otherC.voided_apply_number)) applyList.Add(otherC.voided_apply_number);
                                    }
                                }
                            }
                            else if (other.form_type == "D")
                            {
                                List<other_application_d> otherDList = _otherApplyRepository.GetOtherApplicationD(apply_number);
                                if (otherDList != null && otherDList.Count > 0)
                                {
                                    foreach (other_application_d otherD in otherDList)
                                    {
                                        if (!string.IsNullOrEmpty(otherD.seal_apply_number)) applyList.Add(otherD.seal_apply_number);
                                    }
                                }
                            }
                        }
                        foreach (string apply in applyList)
                        {
                            V_GetAllApplication form = _flowRepository.GetAllApplication(apply);
                            if (form == null) continue;
                            //Issue：103
                            int flow_step = 0;
                            if (form.apply_type == "C")
                            {
                                flow_step = Convert.ToInt16(form.form_type);
                            }
                            else
                            {
                                if (form.form_type == "R") flow_step = 4;
                                if (form.form_type == "AR") flow_step = 12;
                            }
                            //原簽核流程已刪除，取得最新的合約管理人
                            List<ApplicationApproveProcess> contractMaTaskList = FlowStepProcessService.GetOtherApplicationApproveProcessSignerSP(form.apply_type, form.form_type, apply, _flowRepository.GetContractMaTaskStepid(flow_step));
                            if (contractMaTaskList != null && contractMaTaskList.Count > 0)
                            {
                                foreach (ApplicationApproveProcess contractMaTask in contractMaTaskList)
                                {
                                    if (contractMaTask != null && !string.IsNullOrEmpty(contractMaTask.signerEmplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = contractMaTask.signerEmplid, recipient_deptid = contractMaTask.signerDeptid });
                                    }
                                }
                            }
                        }
                        break;
                    //必要揭露人員名單-內部同仁(僅適用其他申請)
                    case "15":
                        List<other_application_expose_person> exposeList = _otherApplyRepository.GetOtherApplicationExposePerson(apply_number);
                        if (exposeList == null || exposeList.Count == 0) continue;

                        List<other_application_expose_person> innerList = exposeList.Where(x => x.expose_type == "01").ToList();
                        if (innerList == null || innerList.Count == 0) continue;

                        foreach (other_application_expose_person inner in innerList)
                        {
                            if (inner != null && !string.IsNullOrEmpty(inner.expose_emplid))
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = inner.expose_emplid, recipient_deptid = GetEmpl(inner.expose_emplid).deptid });
                            }
                        }
                        break;
                    //印鑑保管人(只有合約申請有)
                    case "16":
                        form_application formApply = _formApplyRepository.GetFormApplyInfo(apply_number);
                        if (formApply == null) continue;

                        if (!string.IsNullOrEmpty(formApply.seal_type) && formApply.seal_type == "1" && !string.IsNullOrEmpty(formApply.other_seal_type))
                        {
                            List<SealCustodianManagement> sealList = SealCustodianManagementDataService.Query(new SealCustodianManagementQueryCondition()
                            {
                                SealType = formApply.other_seal_type,
                                EntityId = formApply.entity_id
                            });

                            if (sealList == null || sealList.Count == 0) continue;
                            foreach (SealCustodianManagement seal in sealList)
                            {
                                if (!string.IsNullOrEmpty(seal.EmpId))
                                {
                                    var sealEmp = GetEmpl(seal.EmpId);
                                    if (sealEmp != null)
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = sealEmp.emplid, recipient_deptid = sealEmp.deptid });
                                    }
                                }
                            }
                        }

                        break;
                    //申請人代理人
                    case "17":
                        string fillAgent = _applyPermissionRepository.GetAgenByEmp(application.fill_emplid, application.fill_deptid);
                        if (!string.IsNullOrEmpty(fillAgent))
                        {
                            var agentEmployee = GetEmpl(fillAgent);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        break;
                    //申請人/經辦人/現任聯絡人
                    case "20":
                        //現任聯絡人
                        string incumbent_emp = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp))
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbent_emp, recipient_deptid = application.incumbent_deptid });
                        else
                        {
                            //填單人
                            string fill_emp = application.fill_emplid ?? "";
                            if (!string.IsNullOrEmpty(fill_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill_emp, recipient_deptid = application.fill_deptid });

                            //經辦人
                            string pic_emp = application.pic_emplid ?? "";
                            if (!string.IsNullOrEmpty(pic_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic_emp, recipient_deptid = application.pic_deptid });
                        }
                        break;
                    //合約管理人及其代理人
                    case "22":
                        List<string> applys = new List<string>();
                        //合約申請
                        if (apply_type == "C" || apply_type == "A")
                        {
                            applys.Add(apply_number);
                        }
                        //其他申請C/D類
                        else if (apply_type == "O")
                        {
                            other_application other = _otherApplyRepository.GetOtherApplication(apply_number);
                            if (other.form_type == "C")
                            {
                                List<other_application_c> otherCList = _otherApplyRepository.GetOtherApplicationC(apply_number);
                                if (otherCList != null && otherCList.Count > 0)
                                {
                                    foreach (other_application_c otherC in otherCList)
                                    {
                                        if (!string.IsNullOrEmpty(otherC.voided_apply_number)) applys.Add(otherC.voided_apply_number);
                                    }
                                }
                            }
                            else if (other.form_type == "D")
                            {
                                List<other_application_d> otherDList = _otherApplyRepository.GetOtherApplicationD(apply_number);
                                if (otherDList != null && otherDList.Count > 0)
                                {
                                    foreach (other_application_d otherD in otherDList)
                                    {
                                        if (!string.IsNullOrEmpty(otherD.seal_apply_number)) applys.Add(otherD.seal_apply_number);
                                    }
                                }
                            }
                        }
                        foreach (string apply in applys)
                        {
                            V_GetAllApplication form = _flowRepository.GetAllApplication(apply);
                            if (form == null) continue;
                            int flow_step = 0;
                            if (form.apply_type == "C")
                            {
                                flow_step = Convert.ToInt16(form.form_type);
                            }
                            else
                            {
                                if (form.form_type == "R") flow_step = 4;
                                if (form.form_type == "AR") flow_step = 12;
                            }
                            //原簽核流程已刪除，取得最新的合約管理人
                            List<ApplicationApproveProcess> contractMaTaskList = FlowStepProcessService.GetOtherApplicationApproveProcessSignerSP(form.apply_type, form.form_type, apply, _flowRepository.GetContractMaTaskStepid(flow_step));
                            if (contractMaTaskList != null && contractMaTaskList.Count > 0)
                            {
                                foreach (ApplicationApproveProcess contractMaTask in contractMaTaskList)
                                {
                                    if (contractMaTask != null && !string.IsNullOrEmpty(contractMaTask.signerEmplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = contractMaTask.signerEmplid, recipient_deptid = contractMaTask.signerDeptid });
                                        string signerAgent = _applyPermissionRepository.GetAgenByEmp(contractMaTask.signerEmplid, contractMaTask.signerDeptid);
                                        if (!string.IsNullOrEmpty(signerAgent))
                                        {
                                            var agentEmployee = GetEmpl(signerAgent);
                                            if (agentEmployee != null)
                                            {
                                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    //申請人/現任聯絡人
                    case "23":
                        //現任聯絡人
                        string incumbent_emp_23 = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp_23))
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbent_emp_23, recipient_deptid = application.incumbent_deptid });
                        else
                        {
                            //填單人
                            string fill_emp = application.fill_emplid ?? "";
                            if (!string.IsNullOrEmpty(fill_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fill_emp, recipient_deptid = application.fill_deptid });
                        }
                        break;
                    //經辦人/現任聯絡人
                    case "24":
                        //現任聯絡人
                        string incumbent_emp_24 = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp_24))
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = incumbent_emp_24, recipient_deptid = application.incumbent_deptid });
                        else
                        {
                            //經辦人
                            string pic_emp = application.pic_emplid ?? "";
                            if (!string.IsNullOrEmpty(pic_emp)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = pic_emp, recipient_deptid = application.pic_deptid });
                        }
                        break;
                    //Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = adminEMail, recipient_deptid = adminEMail });
                        break;
                    default:
                        break;
                }
            }

            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }

        /// <summary>
        /// 获取收件人
        /// </summary>
        /// <param name="application"></param>
        /// <param name="apply_type"></param>
        /// <param name="receiver_type"></param>
        /// <param name="inputReceiverList"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public List<MailRecipientResultModel> GetReceiverV2(Elegal.Interface.Api.Common.Model.DBModel.V_GetAllApplication application, string apply_type,
            string receiver_type, List<MailRecipientResultModel> inputReceiverList)
        {
            if (application == null) return [];

            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type) ?? [];
            // 用來儲存收件人的清單
            var recipients = new List<MailRecipientResultModel>();

            // 遍歷 receiverList，根據 Key 判斷收件人
            foreach (var recipientGroup in receiverList.GroupBy(x => x.Key))
            {
                // 解构 recipientGroup 中的 Value
                var allValuesForThisGroup = recipientGroup.SelectMany(x => x.Value).ToList().ToList();

                switch (recipientGroup.Key)
                {
                    // 關卡人員
                    case "01":
                        var stepList = _processRepository.GetApplicationApprove(application.apply_type,
                            application.form_type, application.apply_number);
                        foreach (var step in stepList)
                        {
                            if (!string.IsNullOrEmpty(step.signerEmplid) && allValuesForThisGroup.Any(v =>
                                    v.Equals(step.stepName, StringComparison.OrdinalIgnoreCase)))
                            {
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = step.signerEmplid,
                                    recipient_deptid = step.signerDeptid ?? ""
                                });
                            }
                        }

                        break;
                    // 角色
                    case "02":
                        var pUserRoles = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.p_user_role>()
                            .LeftJoin<Elegal.Interface.Api.Common.Model.SqlSugarModels.ps_sub_ee_lgl_vw_a>((r, e) => r.u_id == e.emplid)
                            .Where((r, e) => allValuesForThisGroup.Select(x => Convert.ToInt32(x)).Contains(r.r_id))
                            .Select((r, e) => new
                            {
                                recipient_emplid = r.u_id,
                                recipient_deptid = e.deptid
                            })
                            .ToList();

                        foreach (var pUserRole in pUserRoles.Where(r => !string.IsNullOrEmpty(r.recipient_emplid)))
                        {
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = pUserRole.recipient_emplid,
                                recipient_deptid = pUserRole.recipient_deptid
                            });
                        }

                        break;
                    // 當關應簽核人員及其代理人
                    case "03":
                        //20250219當傳入當關人員時取傳入值
                        if (inputReceiverList != null && inputReceiverList.Count > 0)
                        {
                            recipients.AddRange(inputReceiverList);
                            // 获取代理人
                            var agentsList = _applyPermissionRepository.GetAgenByEmpToTransaction(inputReceiverList
                                .Select(r => new Interface.Api.Common.Model.SqlSugarModels.ps_sub_ee_lgl_vw_a
                                {
                                    emplid = r.recipient_emplid,
                                    deptid = r.recipient_deptid
                                }).ToList());

                            foreach (var item in agentsList)
                            {
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = item.agent_empid,
                                    recipient_deptid = item.agent_deptid
                                });
                            }
                        }
                        else
                        {
                            var signerList = SqlSugarHelper.Db
                                .Queryable<Interface.Api.Common.Model.SqlSugarModels.flow_step_signer>()
                                .LeftJoin<Interface.Api.Common.Model.SqlSugarModels.sys_agent>((f, a) =>
                                    f.signer_emplid == a.auth_empid && f.signer_deptid == a.auth_deptid &&
                                    a.start_time <= DateTime.UtcNow && a.end_time >= DateTime.UtcNow)
                                .LeftJoin<Elegal.Interface.Api.Common.Model.SqlSugarModels.ps_sub_ee_lgl_vw_a>((f, a, e) => a.agent_empid == e.emplid)
                                .Where((f, a, e) => f.apply_number == application.apply_number)
                                .Select((f, a, e) => new
                                {
                                    recipient_emplid = f.signer_emplid,
                                    recipient_deptid = f.signer_deptid,
                                    agent_emplid = a.agent_empid,
                                    agent_deptid = e.deptid
                                }).ToList();

                            foreach (var signer in signerList)
                            {
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = signer.recipient_emplid,
                                    recipient_deptid = signer.recipient_deptid ?? ""
                                });
                                if (!string.IsNullOrEmpty(signer.agent_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel()
                                    {
                                        recipient_type = recipientGroup.Key,
                                        recipient_emplid = signer.agent_emplid,
                                        recipient_deptid = signer.agent_deptid ?? ""
                                    });
                                }
                            }
                        }

                        break;
                    // 填單人
                    case "04":
                        string fill = application.fill_emplid ?? "";
                        if (!string.IsNullOrEmpty(fill))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = fill,
                                recipient_deptid = application.fill_deptid
                            });
                        break;
                    // 經辦人
                    case "05":
                        string pic = application.pic_emplid ?? "";
                        if (!string.IsNullOrEmpty(pic))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = pic,
                                recipient_deptid = application.pic_deptid
                            });
                        break;
                    // 現任聯絡人
                    case "06":
                        string incumbentEmplid = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbentEmplid))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = incumbentEmplid,
                                recipient_deptid = application.incumbent_deptid ?? ""
                            });
                        break;
                    // 承辦法務
                    case "07":
                        string legalAffairs = application.legal_affairs_emplid ?? "";
                        var ee = SqlSugarHelper.Db.Queryable<Elegal.Interface.Api.Common.Model.SqlSugarModels.ps_sub_ee_lgl_vw_a>().First(x =>
                            x.emplid.Equals(legalAffairs, StringComparison.OrdinalIgnoreCase));
                        if (!string.IsNullOrEmpty(legalAffairs))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = legalAffairs,
                                recipient_deptid = ee.deptid
                            });
                        break;
                    // 總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = legalEMail,
                                recipient_deptid = ""
                            });
                        break;
                    // 被移除的加簽/特殊加簽人員
                    case "09":
                        if (inputReceiverList != null && inputReceiverList.Count > 0)
                            recipients.AddRange(inputReceiverList);
                        break;
                    // 加簽/特殊加簽人員
                    case "10":
                        if (inputReceiverList != null && inputReceiverList.Count > 0)
                        {
                            recipients.AddRange(inputReceiverList);
                        }
                        else
                        {
                            var invitees = SqlSugarHelper.Db
                                .Queryable<Interface.Api.Common.Model.SqlSugarModels.flow_step_signer_invitee>()
                                .Where(x => x.apply_number == application.apply_number)
                                .Select(x => new
                                {
                                    recipient_emplid = x.invitee_emplid,
                                    recipient_deptid = x.invitee_deptid
                                })
                                .ToList();
                            if (invitees != null && invitees.Count > 0)
                            {
                                foreach (var invitee in invitees)
                                {
                                    if (!string.IsNullOrEmpty(invitee.recipient_emplid))
                                    {
                                        recipients.Add(new MailRecipientResultModel()
                                        {
                                            recipient_type = recipientGroup.Key,
                                            recipient_emplid = invitee.recipient_emplid,
                                            recipient_deptid = invitee.recipient_deptid ?? ""
                                        });
                                    }
                                }
                            }
                        }

                        break;
                    // 已簽核過人員
                    case "11":
                        var historys = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.flow_step_history>()
                            .Where(x => x.apply_number == application.apply_number)
                            .Select(x => new
                            {
                                recipient_emplid = x.actual_signer_emplid,
                                recipient_deptid = x.actual_signer_deptid
                            }).ToList();
                        if (historys != null && historys.Count > 0)
                        {
                            foreach (var history in historys)
                            {
                                if (!string.IsNullOrEmpty(history.recipient_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel()
                                    {
                                        recipient_type = recipientGroup.Key,
                                        recipient_emplid = history.recipient_emplid,
                                        recipient_deptid = history.recipient_deptid ?? ""
                                    });
                                }
                            }
                        }

                        break;
                    // 當關簽核人員
                    case "12":
                        var signers = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.flow_step_signer>()
                            .Where(x => x.apply_number == application.apply_number)
                            .Select(x => new
                            {
                                recipient_emplid = x.signer_emplid,
                                recipient_deptid = x.signer_deptid
                            }).ToList();
                        if (signers != null && signers.Count > 0)
                        {
                            foreach (var signer in signers)
                            {
                                if (!string.IsNullOrEmpty(signer.recipient_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel()
                                    {
                                        recipient_type = recipientGroup.Key,
                                        recipient_emplid = signer.recipient_emplid,
                                        recipient_deptid = signer.recipient_deptid ?? ""
                                    });
                                }
                            }
                        }

                        break;
                    // 經辦人代理人
                    case "13":
                        var agent = _applyPermissionRepository.GetAgenByEmpToTransaction(application.pic_emplid,
                            application.pic_deptid);
                        if (agent != null)
                        {
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = agent.agent_empid,
                                recipient_deptid = agent.agent_deptid
                            });
                        }

                        break;
                    // 合約管理人(僅適用合約申請+建檔)                    
                    case "22":
                    case "14":
                        var applyList = new List<string>();
                        // 合約申請+建檔
                        if (apply_type == "C" || apply_type == "A")
                        {
                            applyList.Add(application.apply_number);
                        }
                        // 其他申請C/D類
                        else if (apply_type == "O")
                        {
                            var otherApplication = SqlSugarHelper.Db
                                .Queryable<Interface.Api.Common.Model.SqlSugarModels.other_application>()
                                .First(x => x.apply_number == application.apply_number);
                            if (otherApplication?.form_type == "C")
                            {
                                var otherCList = SqlSugarHelper.Db
                                    .Queryable<Interface.Api.Common.Model.SqlSugarModels.other_application_c>()
                                    .Where(x => x.apply_number == application.apply_number).ToList();
                                foreach (var otherC in otherCList)
                                {
                                    if (!string.IsNullOrEmpty(otherC.voided_apply_number))
                                        applyList.Add(otherC.voided_apply_number);
                                }
                            }
                            else if (otherApplication?.form_type == "D")
                            {
                                var otherDList = SqlSugarHelper.Db
                                    .Queryable<Interface.Api.Common.Model.SqlSugarModels.other_application_d>()
                                    .Where(x => x.apply_number == application.apply_number).ToList();
                                foreach (var otherD in otherDList)
                                {
                                    if (!string.IsNullOrEmpty(otherD.seal_apply_number))
                                        applyList.Add(otherD.seal_apply_number);
                                }
                            }
                        }

                        var formList = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.V_GetAllApplication>()
                            .Where(x => applyList.Contains(x.apply_number)).ToList();

                        var flow_step_list = formList.Select(x => new
                        {
                            apply_number = x.apply_number,
                            flow_step = x.apply_type == "C" ? Convert.ToInt16(x.form_type) :
                                x.form_type == "R" ? 4 :
                                x.form_type == "AR" ? 12 : 0
                        }).ToList();

                        // 合約管理人關卡 StepID
                        var flow_steps = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.flow_step>()
                            .Where(x => flow_step_list.Any(f => f.flow_step == x.step_id) &&
                                        x.step_name == "CONTRACT_MA_TASK")
                            .Select(x => new
                            {
                                flow_id = x.flow_id,
                                step_id = x.step_id
                            }).ToList();

                        foreach (var form in formList)
                        {
                            //Issue：103
                            int flow_step = flow_step_list.FirstOrDefault(x => x.apply_number == form.apply_number)
                                ?.flow_step ?? 0;
                            var spName =
                                PublicHelper.GetApplicationApproveProcessSpName(form.apply_type, form.form_type);
                            if (string.IsNullOrEmpty(spName))
                                throw new Exception(
                                    ActionFilter.GetMultilingualValue("custom:messageTitle:dataNotexist"));
                            string sql = $@"EXEC " + spName + " @lang, @apply_number, @applicationType, @flowStepID";
                            var contractMaTaskList = SqlSugarHelper.Db.Ado.SqlQuery<ApplicationApproveProcess>(sql, new
                            {
                                lang = MvcContext.UserInfo.logging_locale,
                                apply_number = form.apply_number,
                                applicationType = form.apply_type,
                                flowStepID = flow_steps.FirstOrDefault(x => x.flow_id == flow_step)?.step_id ?? 0
                            });
                            if (contractMaTaskList != null && contractMaTaskList.Count > 0)
                            {
                                var agentByList = _applyPermissionRepository.GetAgenByEmpToTransaction(
                                    contractMaTaskList.Select(x =>
                                        new Interface.Api.Common.Model.SqlSugarModels.ps_sub_ee_lgl_vw_a
                                        {
                                            emplid = x.signerEmplid,
                                            deptid = x.signerDeptid
                                        }).ToList());

                                foreach (var item in contractMaTaskList.Where(x =>
                                             !string.IsNullOrEmpty(x.signerEmplid)))
                                {
                                    recipients.Add(new MailRecipientResultModel()
                                    {
                                        recipient_type = recipientGroup.Key,
                                        recipient_emplid = item.signerEmplid!,
                                        recipient_deptid = item.signerDeptid ?? ""
                                    });
                                    // 代理人
                                    if (recipientGroup.Key == "22")
                                    {
                                        var agentBy = agentByList.FirstOrDefault(x =>
                                            x.auth_empid == item.signerEmplid && x.auth_deptid == item.signerDeptid);
                                        if (agentBy != null)
                                        {
                                            recipients.Add(new MailRecipientResultModel()
                                            {
                                                recipient_type = recipientGroup.Key,
                                                recipient_emplid = agentBy.agent_empid,
                                                recipient_deptid = agentBy.agent_deptid
                                            });
                                        }
                                    }
                                }
                            }
                        }

                        break;
                    // 必要揭露人員名單-內部同仁(僅適用其他申請)
                    case "15":
                        var exposeList = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.other_application_expose_person>()
                            .LeftJoin<ps_sub_ee_lgl_vw_a>((o, e) => o.expose_emplid == e.emplid)
                            .Where((o, e) => o.apply_number == application.apply_number && o.expose_type == "01")
                            .Select((o, e) => new
                            {
                                expose_emplid = o.expose_emplid,
                                expose_deptid = e.deptid
                            }).ToList();
                        if (exposeList != null && exposeList.Count > 0)
                        {
                            foreach (var expose in exposeList)
                            {
                                if (!string.IsNullOrEmpty(expose.expose_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel()
                                    {
                                        recipient_type = recipientGroup.Key,
                                        recipient_emplid = expose.expose_emplid,
                                        recipient_deptid = expose.expose_deptid ?? ""
                                    });
                                }
                            }
                        }

                        break;
                    // 印鑑保管人(只有合約申請有)
                    case "16":
                        var formApply = SqlSugarHelper.Db
                            .Queryable<Interface.Api.Common.Model.SqlSugarModels.form_application>()
                            .First(x => x.apply_number == application.apply_number);
                        if (formApply == null) continue;

                        if (!string.IsNullOrEmpty(formApply.seal_type) && formApply.seal_type == "1" &&
                            !string.IsNullOrEmpty(formApply.other_seal_type))
                        {
                            var seal_custodian_management_list = SqlSugarHelper.Db
                                .Queryable<Interface.Api.Common.Model.SqlSugarModels.seal_custodian_management>()
                                .LeftJoin<ps_sub_ee_lgl_vw_a>((s, e) => s.emp_id == e.emplid)
                                .Where((s, e) =>
                                    s.entity_id == formApply.entity_id && s.seal_type == formApply.other_seal_type)
                                .Select((s, e) => new
                                {
                                    recipient_emplid = s.emp_id,
                                    recipient_deptid = e.deptid
                                }).ToList();
                            foreach (var seal in seal_custodian_management_list)
                            {
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = seal.recipient_emplid,
                                    recipient_deptid = seal.recipient_deptid
                                });
                            }
                        }

                        break;
                    // 申請人代理人
                    case "17":
                        var agentInfo =
                            _applyPermissionRepository.GetAgenByEmpToTransaction(application.fill_emplid,
                                application.fill_deptid);
                        if (agentInfo != null)
                        {
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = agentInfo.agent_empid,
                                recipient_deptid = agentInfo.agent_deptid
                            });
                        }

                        break;
                    // 申請人/經辦人/現任聯絡人
                    case "20":
                        //現任聯絡人
                        string incumbent_emp = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = incumbent_emp,
                                recipient_deptid = application.incumbent_deptid
                            });
                        else
                        {
                            //填單人
                            string fill_emp = application.fill_emplid ?? "";
                            if (!string.IsNullOrEmpty(fill_emp))
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = fill_emp,
                                    recipient_deptid = application.fill_deptid
                                });

                            //經辦人
                            string pic_emp = application.pic_emplid ?? "";
                            if (!string.IsNullOrEmpty(pic_emp))
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = pic_emp,
                                    recipient_deptid = application.pic_deptid
                                });
                        }

                        break;
                    // 申請人/現任聯絡人
                    case "23":
                        //現任聯絡人
                        string incumbent_emp_23 = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp_23))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = incumbent_emp_23,
                                recipient_deptid = application.incumbent_deptid
                            });
                        else
                        {
                            //填單人
                            string fill_emp = application.fill_emplid ?? "";
                            if (!string.IsNullOrEmpty(fill_emp))
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = fill_emp,
                                    recipient_deptid = application.fill_deptid
                                });
                        }

                        break;
                    // 經辦人/現任聯絡人
                    case "24":
                        // 現任聯絡人
                        string incumbent_emp_24 = application.incumbent_emplid ?? "";
                        if (!string.IsNullOrEmpty(incumbent_emp_24))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = incumbent_emp_24,
                                recipient_deptid = application.incumbent_deptid
                            });
                        else
                        {
                            // 經辦人
                            string pic_emp = application.pic_emplid ?? "";
                            if (!string.IsNullOrEmpty(pic_emp))
                                recipients.Add(new MailRecipientResultModel()
                                {
                                    recipient_type = recipientGroup.Key,
                                    recipient_emplid = pic_emp,
                                    recipient_deptid = application.pic_deptid
                                });
                        }

                        break;
                    // Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail))
                            recipients.Add(new MailRecipientResultModel()
                            {
                                recipient_type = recipientGroup.Key,
                                recipient_emplid = adminEMail,
                                recipient_deptid = adminEMail
                            });
                        break;
                    default:
                        break;
                }
            }

            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }

        #region 取得收件者

        #endregion


        /// <summary>
        /// 取得經辦人轉單收件者
        /// </summary>
        public static List<MailRecipientResultModel> GetTransferReceiver(string transfer_pic_number, string receiver_type)
        {
            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);

            // 用來儲存收件人的清單
            List<MailRecipientResultModel> recipients = new List<MailRecipientResultModel>();

            TransferLegalHistory transferLegalHistory = null;
            TransferPicMain transferPicMain = null;
            FlowStepSigner flowStep = null;
            List<flow_step_signer> signerList = null;
            //承辦法務轉單
            if (transfer_pic_number.StartsWith("TL"))
                transferLegalHistory = TransferLegalHistoryDataService.Find(new TransferLegalHistoryQueryCondition()
                {
                    TransferPicNumber = transfer_pic_number
                });
            //經辦人轉單
            else if (transfer_pic_number.StartsWith("T"))
            {
                transferPicMain = TransferPicMainDataService.FindByKey(transfer_pic_number);

                flowStep = FlowStepSignerDataService.Find(new FlowStepSignerQueryCondition
                {
                    ApplyNumber = transfer_pic_number,
                });
                signerList = _flowRepository.GetFlowStepSigner(transfer_pic_number);
            }

            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {
                    //當關應簽核人及其代理人
                    case "03":
                        if (signerList != null && signerList.Count > 0)
                        {
                            foreach (flow_step_signer signer in signerList)
                            {
                                if (!string.IsNullOrEmpty(signer.signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = signer.signer_emplid, recipient_deptid = signer.signer_deptid });
                                    string signerAgent = _applyPermissionRepository.GetAgenByEmp(signer.signer_emplid, signer.signer_deptid);
                                    if (!string.IsNullOrEmpty(signerAgent))
                                    {
                                        var agentEmployee = GetEmpl(signerAgent);
                                        if (agentEmployee != null)
                                        {
                                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                                        }
                                    }
                                }
                            }
                        }
                        break;
                    //申請人
                    case "04":
                        if (transfer_pic_number.StartsWith("TL") && transferLegalHistory != null)
                        {
                            var agentEmployee = GetEmpl(transferLegalHistory.CreateUser);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        else if (transferPicMain != null && !string.IsNullOrEmpty(transferPicMain.FillEmplid))
                        {
                            var fillEmployee = GetEmpl(transferPicMain.FillEmplid);
                            if (fillEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = fillEmployee.emplid, recipient_deptid = fillEmployee.deptid });
                            }
                        }
                        break;
                    //當關應簽核人
                    case "12":
                        if (signerList != null && signerList.Count > 0)
                        {
                            foreach (flow_step_signer signer in signerList)
                            {
                                if (!string.IsNullOrEmpty(signer.signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = signer.signer_emplid, recipient_deptid = signer.signer_deptid });
                                }
                            }
                        }
                        break;
                    //交接人
                    case "33":
                        if (transfer_pic_number.StartsWith("TL") && transferLegalHistory != null)
                        {
                            var agentEmployee = GetEmpl(transferLegalHistory.HandoverEmplid);
                            if (agentEmployee != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agentEmployee.emplid, recipient_deptid = agentEmployee.deptid });
                            }
                        }
                        else if (transferPicMain != null && !string.IsNullOrEmpty(transferPicMain.HandoverEmplid))
                        {
                            var emp = GetEmpl(transferPicMain.HandoverEmplid);
                            if (emp != null)
                            {
                                recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = emp.emplid, recipient_deptid = emp.deptid });
                            }
                        }
                        break;
                    //已簽核人
                    case "11":
                        List<flow_step_history> historyList = _flowRepository.GetFlowStepHistory(transfer_pic_number);
                        if (historyList != null && historyList.Count > 0)
                        {
                            foreach (flow_step_history history in historyList)
                            {
                                if (!string.IsNullOrEmpty(history.actual_signer_emplid))
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = history.actual_signer_emplid, recipient_deptid = history.actual_signer_deptid });
                                }
                            }
                        }
                        break;
                    //原經辦部門主管
                    case "35":
                        if (transferPicMain != null)
                        {
                            PsSubOgLglVwA psSubOgLglVwA = PsSubOgLglVwADataService.FindByKey(transferPicMain.TransferDeptid);
                            if (psSubOgLglVwA != null && !string.IsNullOrEmpty(psSubOgLglVwA.ManagerId))
                            {
                                var manager = GetEmpl(psSubOgLglVwA.ManagerId);
                                if (manager != null)
                                {
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = manager.emplid, recipient_deptid = manager.deptid });
                                }
                            }

                        }
                        break;
                    //操作人
                    case "21":
                        var current_login = GetEmpl(MvcContext.UserInfo.current_emp);
                        if (current_login != null)
                        {
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = current_login.emplid, recipient_deptid = current_login.deptid });
                        }
                        break;
                    //角色
                    case "02":
                        PUserRoleDataRepository pUserRoleDataRepository = new PUserRoleDataRepository();
                        List<PUserRole> pUserRoles = pUserRoleDataRepository.Query(new PUserRoleQueryCondition()
                        {
                            SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = receiver.Value, Field = "r_id" }] }
                        });
                        if (pUserRoles != null && pUserRoles.Count > 0)
                        {
                            foreach (PUserRole role in pUserRoles)
                            {
                                if (!string.IsNullOrEmpty(role.UId))
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = role.UId, recipient_deptid = GetEmpl(role.UId).deptid });
                            }
                        }
                        break;
                    //Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = adminEMail, recipient_deptid = adminEMail });
                        break;
                    //總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalEMail, recipient_deptid = "" });
                        break;
                    default:
                        break;
                }
            }
            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }

        #region 紙本進度追蹤 郵件
        /// <summary>
        /// 寄信  紙本進度追蹤 郵件
        /// </summary>
        /// <param name="trackIds">即將批量發送的單號集合</param>
        /// <param name="userEmailConten">郵件模板</param>
        /// <returns></returns>
        public static List<UserEmailTrackResult> SendPaperTrackingMail(List<string> trackIds, UserEmailContent userEmailConten)
        {

            List<UserEmailTrackResult> resultList = trackIds?.Select(e => new UserEmailTrackResult() { succeed = false, TrackId = e })?.ToList() ?? new List<UserEmailTrackResult>();//用来记录成功的单子邮件发送结果;
            try
            {
                if (trackIds == null || trackIds.Count == 0) return resultList;
                if (userEmailConten == null) return resultList;

                //一次性获取所有单据  的一些信息,僅僅 PaperTrackingInfo 對應的字段
                List<PaperTrackingInfo> ptiList = PaperTrackingService.GetBePaperTrackingInfo(trackIds);
                string massage = ActionFilter.GetMultilingualValue("custom:messageContent:nofeSendMail");
                foreach (var item in ptiList)
                {
                    var rl = resultList.FirstOrDefault(e => e.TrackId == item.track_id);
                    if (rl != null)
                    {
                        rl.apply_number = item.apply_number; //單號
                        if (!item.is_send_mail)
                        {
                            /// sit 485  郵件排除： “啟用合約/建檔郵件通知”設定為停用的郵件
                            /// 主體是否需要發送郵件，不發郵件的主體直接記錄到返回值裡邊，
                            rl.succeed = false;
                            rl.Massage = massage;
                            rl.StatusCode = 2;
                        }

                    }
                }

                //需要发送的主体
                var feisSendMailList = ptiList.Where(e => e.is_send_mail).ToList();
                List<PaperTrackingReceiver> sjrList = GetPaperTrackingReceiver(feisSendMailList, userEmailConten.MailReType);//獲取這一批單據的收件人
                List<PaperTrackingReceiver> ccList = GetPaperTrackingReceiver(feisSendMailList, userEmailConten.MailCcType);//獲取這一批單據的抄送人

                //根據單號把收件人與抄送人匹配到一起
                var list = (from s in sjrList
                            join c in ccList on s.track_id equals c.track_id
                            select new
                            {
                                s.track_id, //單據
                                EReceiver = s.EmailListStr, //收件人郵箱
                                sjrList = s.ReceiverList,//收件人集合，邮件内如果有 收件人字段，要根据此工号获取收件人姓名
                                ECc = c.EmailListStr, //抄送人郵箱
                            }).ToList();

                foreach (var groupItem in list.GroupBy(e => new { e.EReceiver, e.ECc }))
                { //根據收件人與抄送人分組，擁有相同的收件人與抄送人的單據為一個郵件
                    List<string> thatGIds = groupItem.Select(e => e.track_id).ToList();//當前組內的單據集合
                    try
                    {
                        var sjr = groupItem.FirstOrDefault()?.sjrList ?? new List<EmpidEmailDic>();
                        //當前組內的數據生成郵件內容
                        UserEmailContent sysEmailContent = GetPaperTrackingMailContent(thatGIds, userEmailConten, sjr);
                        if (sysEmailContent == null) continue; //如果沒有，跳過本組發送
                        SysEmail se = new SysEmail();
                        #region 默認參數
                        ////郵件發送人員
                        se.EReceiver = groupItem.Key.EReceiver;
                        ////拼接郵件抄送人員
                        se.ECc = groupItem.Key.ECc;
                        //郵件標題
                        se.ESubject = sysEmailContent.MailSubject;
                        //郵件內容
                        se.EContent = sysEmailContent.MailContent;
                        se.ESendtime = DateTime.UtcNow;
                        se.ESendnum = 0;
                        se.EIssend = (int)YesOrNoUtils.No;
                        se.EType = sysEmailContent.MailType ?? userEmailConten.MailType;
                        #endregion
                        //發送郵件
                        if (!string.IsNullOrEmpty(se.EReceiver) || !string.IsNullOrEmpty(se.ECc))
                        {
                            SysEmailDataService.Create(se);
                            //发送成功后往结果集里边记录此邮件发送情况
                            foreach (var item in resultList.Where(e => thatGIds.Contains(e.TrackId)))
                            {
                                item.succeed = true;
                                item.Csr = groupItem.Key.ECc;
                                item.Sjr = groupItem.Key.EReceiver;
                                item.Massage = string.Empty;
                                item.StatusCode = 1;
                            };
                        }

                    }
                    catch (Exception ex)
                    {
                        //報錯後后往结果集里边记录此邮件发送情况
                        foreach (var item in resultList.Where(e => thatGIds.Contains(e.TrackId)))
                        {
                            item.succeed = false;
                            item.Csr = string.Empty;
                            item.Sjr = string.Empty;
                            item.Massage = ex.Message;
                            item.StatusCode = 3;
                        };
                    }

                }
                return resultList; //返回成功的单子发送结果
            }
            catch
            {
                return resultList;
            }
        }
        /// <summary>
        /// 批量 獲取紙本進度追蹤 收件人/或者抄送人
        /// </summary>
        /// <returns></returns>
        private static List<PaperTrackingReceiver> GetPaperTrackingReceiver(List<PaperTrackingInfo> list, string receiver_type)
        {
            List<string> numbers = list.Select(e => e.track_id).ToList();
            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);


            // 用來儲存所有紙本批次收件人的清單
            List<PaperTrackingReceiver> recipients = numbers.Select(e => new PaperTrackingReceiver()
            {
                track_id = e,  //紙本進度追蹤單據
                EmailListStr = string.Empty,
                ReceiverList = new List<EmpidEmailDic>(), //該單據下所包含的收件人集合
            }).ToList();

            #region 为  查询該郵件模板下設定的收件人
            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {
                    //關卡人員
                    case "01":
                        //一次性查询出所有单据对应的關卡收件人
                        List<PaperTrackingStepEmpid> paperTrackingStepEmpids = PaperTrackingService.GetBeStepPaperTracking(numbers, receiver.Value);
                        paperTrackingStepEmpids.ForEach(ptse =>  //循環處理所有的查詢結果
                        {
                            if (!string.IsNullOrWhiteSpace(ptse.emp_id))
                            {
                                //把每一个收件人按照单号匹配到对应的单据下
                                var thisrecipient = recipients.FirstOrDefault(r => r.track_id == ptse.track_id);//當前的單據
                                thisrecipient?.ReceiverList.Add(new EmpidEmailDic(ptse.emp_id, string.Empty));
                            }
                        });
                        break;
                    //申請人/經辦人/現任聯絡人
                    case "20":
                        list.ForEach(pti => //循環處理所有的查詢結果
                        {
                            var thisrecipient = recipients.FirstOrDefault(e => e.track_id == pti.track_id);//當前的單據
                            if (!string.IsNullOrWhiteSpace(pti.incumbent_emplid))
                            {  //如果有現任聯絡人就取現任聯絡人
                                thisrecipient?.ReceiverList.Add(new EmpidEmailDic(pti.incumbent_emplid, string.Empty));
                            }
                            else
                            {
                                HashSet<string> emps = new HashSet<string>(); //經辦人與申請人 ,用唯一值集合來處理 
                                emps.Add(pti.pic_emplid);
                                emps.Add(pti.fill_emplid);
                                thisrecipient?.ReceiverList.AddRange(emps.Select(e => new EmpidEmailDic(e, string.Empty)));
                            }
                        });
                        break;
                    case "21": //操作人
                        var currentUseremp = MvcContext.UserInfo?.current_emp;
                        if (!string.IsNullOrWhiteSpace(currentUseremp))
                        {
                            recipients.ForEach(item => //為每個單據添加收件人
                            {
                                item.ReceiverList.Add(new EmpidEmailDic(currentUseremp, string.Empty));
                            });
                        }
                        break;
                    case "07": //承辦法務
                        list.ForEach(pti => //循環處理所有的查詢結果
                        {
                            if (!string.IsNullOrWhiteSpace(pti.legal_affairs_emplid))
                            {
                                var thisrecipient = recipients.FirstOrDefault(e => e.track_id == pti.track_id);//當前的單據
                                thisrecipient?.ReceiverList.Add(new EmpidEmailDic(pti.incumbent_emplid, string.Empty));
                            }

                        });
                        break;
                    case "02": //角色
                        var ruempls = PaperTrackingService.GetEmpbyRids(receiver.Value);//一次性查出
                        if (ruempls.Count > 0)
                        {
                            var listruempls = ruempls.Select(empid => new EmpidEmailDic(empid, string.Empty));
                            recipients.ForEach(item => //為每個單據添加收件人
                            {
                                item.ReceiverList.AddRange(listruempls);
                            });
                        }
                        break;
                    case "08": //legalEMail
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail))
                        {
                            recipients.ForEach(item =>
                            {
                                item.ReceiverList.Add(new EmpidEmailDic(string.Empty, legalEMail));
                            });
                        };
                        break;
                    case "26": //adminEMail
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail))
                        {
                            recipients.ForEach(item =>
                            {
                                item.ReceiverList.Add(new EmpidEmailDic(string.Empty, adminEMail));
                            });
                        }
                        break;
                    default:
                        break;
                }

            }
            #endregion


            #region //為每一條單據計算出  EmailListStr 屬性
            HashSet<string> empls = new HashSet<string>();//存儲所有可能出現的工號,用唯一值集合存儲,Add默認是去重的
            recipients.ForEach(item => //循環單據
            {
                item.ReceiverList.ForEach(itemR =>//循環單據下的收件人
                {
                    empls.Add(itemR.Emplid);
                });
            });
            //一次性根據工號去數據庫查詢出所有的郵箱; 一次性查詢避免數據庫壓力
            List<FormEmpInfo> userList = PaperTrackingService.GetEmailByEmplids(empls.ToList());

            recipients.ForEach(item => //循環單據
            {
                //直接使用C#查詢語句來匹配 為 每一個收件人匹配對應的郵箱;
                (from ed in item.ReceiverList
                 join u in userList on ed.Emplid equals u.emplid
                 where !string.IsNullOrWhiteSpace(ed.Emplid) //沒有工號的不需要匹配，比如 adminEMail 是直接設置的 Email 字段
                 select new { ed, u.email_address_a, u.name_a, u.name }) //獲得匹配結果
                 .ToList()
                 .ForEach(litem => //對匹配結果進行處理
                 {  //對匹配結果賦值處理,給 EmpidEmailDic 對象的Email 字段賦值
                     if (!string.IsNullOrEmpty(litem.email_address_a) && litem.email_address_a != "NULL")
                     {
                         litem.ed.Email = litem.email_address_a;
                     }
                     var name = (litem.name ?? string.Empty);
                     var namea = (litem.name_a ?? string.Empty);
                     litem.ed.Name = name + "(" + namea + ")"; //並且給姓名字段賦值
                 });

                //用該筆單子的所有收件郵箱生成 EmailListStr 字符串
                List<string> emails = item.ReceiverList
                                      .Select(e => e.Email) //取該單據將要收件的郵箱
                                      .Where(e => !string.IsNullOrWhiteSpace(e) && e != "null" && e != "NULL") //去掉空的
                                      .Distinct() //去重一把
                                      .OrderBy(e => e) //排序一把避免兩筆單子的郵箱  a,b與b,a到時候分組分不到一組,讓字符串順序一致
                                      .ToList();
                item.EmailListStr = string.Join(";", emails);
            });

            #endregion


            return recipients;


        }




        /// <summary>
        /// 根据要发送的单据主键与邮件模板，获取邮件内容与主体
        /// </summary>
        /// <param name="trackIds">分组后要发送的单据</param>
        /// <param name="userEmailConten">邮件模板</param>
        /// <param name="sjr">用来获取收件人姓名</param>
        /// <returns>返回值(邮件主体,邮件内容)</returns>
        private static UserEmailContent GetPaperTrackingMailContent(List<string> trackIds, UserEmailContent userEmailContent, List<EmpidEmailDic> sjr)
        {
            UserEmailContentDataRepository emailContentRepository = new UserEmailContentDataRepository();
            UserEmailDictionaryDataRepository emailDictionaryRepository = new UserEmailDictionaryDataRepository();


            List<UserEmailDictionary> userEmailDictionaries = emailDictionaryRepository.Query(new UserEmailDictionaryQueryCondition()
            {
                FieldType = userEmailContent.FuncModule
            });



            ///获取当前邮件里的隐藏码的字段设定
            List<hidden_code_dictionary> hcds = _repository.GetHiddenCodeByEmailFuncModule(userEmailContent.FuncModule);



            List<MailContentDataResultModel> mailContentDataList = new List<MailContentDataResultModel>();

            #region 處理需要根據單號查詢的字段
            //字段根据数据来源分组，同一个来源一次性查询，避免多次查询
            var group = userEmailDictionaries.Where(e => !string.IsNullOrWhiteSpace(e.FieldSource)).GroupBy(e => new { e.FieldSource, e.SourceType }).ToList();
            foreach (var groupItem in group)//循環組
            {
                if (groupItem.Key.SourceType == "V")
                {  //目前 紙本進度追蹤郵件只有V
                    var dt = PaperTrackingService.GetEmailContentByFieldSources(trackIds, groupItem.Key.FieldSource);
                    if (hcds != null && hcds.Count > 0) dt = HiddenValueConvertHelper.HiddenTableFiled(dt, hcds); //处理隐码
                    var row = dt.GetRow(0); //郵件內容與標題的字段直接取第一筆數據 2025/1/15早會說的
                    if (row == null) continue;//沒查到一條數據，則直接跳過這個組
                    //這裡默認視為只有一筆直接為字段賦值
                    foreach (var item in groupItem)
                    {
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_zh",
                            column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_zh")

                        });
                        mailContentDataList.Add(new MailContentDataResultModel()
                        {
                            column_name = item.FieldCode + "_en",
                            column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_en")

                        });
                        if (item.HasLinkurl.HasValue && item.HasLinkurl.Value == 1)
                        {
                            mailContentDataList.Add(new MailContentDataResultModel()
                            {
                                column_name = item.FieldCode + "_link",
                                column_value = PaperTrackingService.GetRowValue(row, item.FieldCode + "_link")

                            });
                        }
                    }

                }
            }
            #endregion

            #region 處理那些郵件中不需要根據單號去數據庫查詢的字段（這裡是收件人，與 貨件轉運情形查詢; 

            foreach (var item in userEmailDictionaries.Where(e => string.IsNullOrWhiteSpace(e.FieldSource)))
            {
                if (item.FieldCode == "recipient")//收件人
                {
                    var sjrName = string.Join(",", sjr.Select(e => e.Name).Distinct().ToList());
                    mailContentDataList.Add(new MailContentDataResultModel()
                    {
                        column_name = "recipient_zh",
                        column_value = sjrName
                    });
                    mailContentDataList.Add(new MailContentDataResultModel()
                    {
                        column_name = "recipient_en",
                        column_value = sjrName

                    });
                }

                if (item.FieldCode == "se_url") //貨件轉運情形查詢
                {
                    mailContentDataList.Add(new MailContentDataResultModel()
                    {
                        column_name = "se_url_zh",
                        column_value = "貨件轉運情形查詢"
                    });
                    mailContentDataList.Add(new MailContentDataResultModel()
                    {
                        column_name = "se_url_en",
                        column_value = "貨件轉運情形查詢"

                    });
                    if (item.HasLinkurl.HasValue && item.HasLinkurl.Value == 1)
                    {  //如果此字段是個鏈接
                        string value_link = AppSettingHelper.Configuration["consignmentNumber"] ?? string.Empty;
                        if (!string.IsNullOrEmpty(value_link))
                        {
                            mailContentDataList.Add(new MailContentDataResultModel()
                            {
                                column_name = "se_url" + "_link",
                                column_value = value_link
                            });
                        }
                    }
                }

            }
            #endregion


            #region //根據模板創建一個新的返回值對象  (避免直接操作入參   userEmailContent ,因為入參  userEmailContent 僅僅提供郵件模版設定)
            var res = new UserEmailContent()
            {  //創建一個新對象作為返回值 --這裡僅賦值部分需要用到的字段
                MailSubject = userEmailContent.MailSubject,//郵件標題
                MailContent = userEmailContent.MailContent, //郵件內容
                MailType = userEmailContent.MailType,
                HavingTable = userEmailContent.HavingTable, //後邊處理表格用的
                TableJson = userEmailContent.TableJson,//後邊處理表格用的

            };
            //對返回對象 郵件內容與郵件模板進行字符串替換處理
            foreach (var data in mailContentDataList)
            {
                //他方欄位特殊處理
                if (data.column_name == "other_party_zh") data.column_value = ConvertJsonToCommaSeparated(data.column_value);
                if (data.column_name == "other_party_en") data.column_value = ConvertJsonToCommaSeparated(data.column_value);

                res.MailSubject = res.MailSubject.Replace($"{{{data.column_name}}}", data.column_value);
                res.MailContent = res.MailContent.Replace($"{{{data.column_name}}}", data.column_value);
            }

            //對返回值對象裡如果包含table則進行處理 (這裡會直接對res進行操作)
            if (userEmailContent.HavingTable == 1)
                SetPaperTrackingMailTableContent(trackIds, res, userEmailDictionaries, hcds);
            #endregion
            return res;
        }





        /// <summary>
        /// 设定邮件内table （這裡會直接對入參 userEmailContent 進行操作）
        /// </summary>
        /// <param name="trackIds"></param>
        /// <param name="userEmailContent"></param>
        /// <param name="userEmailDictionaries"></param>
        /// <param name="hcds">該郵件需要處理的隱碼字段</param>
        private static void SetPaperTrackingMailTableContent(List<string> trackIds, UserEmailContent userEmailContent, List<UserEmailDictionary> userEmailDictionaries, List<hidden_code_dictionary> hcds)
        {
            var tableJsons = JsonConvert.DeserializeObject<List<TableJson>>(userEmailContent.TableJson ?? "") ?? new List<TableJson>();

            foreach (var table in tableJsons)
            {
                if (userEmailContent.MailContent.Contains(table.tableId))
                {
                    StringBuilder stringBuilder = new StringBuilder();
                    stringBuilder.AppendLine("<table border=\"1\" style=\"border-collapse: collapse;border: 1px solid #ccc;text-align: left;font-size: 12px;\">");
                    stringBuilder.AppendLine("  <tbody>");
                    stringBuilder.AppendLine("      <tr>");
                    foreach (var col in table.columns)
                    {
                        var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                        if (field == null) continue;
                        var title = table.lang.Equals("ZH-TW") ? field?.FieldCname : field?.FieldEname;
                        stringBuilder.AppendLine($"          <td width=\"{(col.Equals("{other}") ? "400px" : "200px")}\">{title}</td>");
                    }
                    stringBuilder.AppendLine("      </tr>");

                    DataTable dt = PaperTrackingService.GetEmailContentByFieldSources(trackIds, "V_PaperTrackWorkMail"); //直接写死数据来自视图 V_PaperTrackWorkMail

                    #region 表格內容隱碼數據處理
                    if (hcds != null && hcds.Count > 0) dt = dt.HiddenTableFiled(hcds);
                    #endregion


                    for (int i = 0; i < dt.Rows.Count; i++)
                    {
                        var row = dt.GetRow(i);
                        if (row == null)
                            continue;

                        stringBuilder.AppendLine("      <tr>");
                        StringBuilder tbBuilder = new StringBuilder();
                        foreach (var col in table.columns)
                        {
                            var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                            if (field == null) continue;
                            var title = $"{{{field.FieldCode}_{(table.lang.Equals("ZH-TW") ? "zh" : "en")}}}";
                            tbBuilder.AppendLine($"          <td>{title}</td>");
                        }

                        foreach (var col in table.columns)
                        {
                            var field = userEmailDictionaries.FirstOrDefault(f => $"{{{f.FieldCode}}}".Equals(col));
                            if (field != null)
                            {
                                string langType = table.lang.Equals("ZH-TW") ? "_zh" : "_en";
                                var zhKey = $"{{{field.FieldCode + langType}}}";
                                var value = string.Empty;
                                if (dt.Columns.Contains(field.FieldCode + langType))
                                { //如果是视图中存在的字段
                                    if (field.HasLinkurl.HasValue
                                        && field.HasLinkurl.Value == 1
                                        && !string.IsNullOrWhiteSpace(row?.GetRowValue(field.FieldCode + "_link")?.ToString())
                                        ) //这个字段是链接(並且連接有值)
                                    {

                                        value = @$" <a title=""點擊此處跳轉"" href=""{row?.GetRowValue(field.FieldCode + "_link")?.ToString()}"" style=""color:'';font-weight:''"">
                                                {row?.GetRowValue(field.FieldCode + langType)?.ToString()}
                                        </a>";
                                    }
                                    else
                                    {
                                        value = row?.GetRowValue(field.FieldCode + langType)?.ToString();
                                    }
                                }



                                //他方欄位特殊處理
                                if ((zhKey == "{other_party_zh}" || zhKey == "{other_party_en}") && !string.IsNullOrWhiteSpace(value)) value = ConvertJsonToCommaSeparated(value);

                                //处理特殊栏位
                                if (field.FieldCode == "recipient")//收件人
                                {
                                    //（從業務上來說，收件人字段一般不會出現在表格列中,萬一被設置了，就顯示 string.Empty）
                                    var sjrName = string.Empty; //string.Join(",", sjr.Select(e => e.Name).Distinct().ToList());
                                    value = sjrName;
                                }

                                if (field.FieldCode == "se_url") //貨件轉運情形查詢
                                {
                                    string value_link = AppSettingHelper.Configuration["consignmentNumber"] ?? string.Empty;
                                    if (!string.IsNullOrWhiteSpace(value_link))
                                    {
                                        value = @$" <a title=""點擊此處跳轉"" href=""{value_link}"" style=""color:'';font-weight:''"">
                                            貨件轉運情形查詢
                                        </a>";
                                    }
                                    else
                                    {
                                        value = "貨件轉運情形查詢";
                                    }


                                }
                                tbBuilder = tbBuilder.Replace(zhKey, value);
                            }
                        }

                        stringBuilder.Append(tbBuilder);
                        stringBuilder.AppendLine("      </tr>");
                    }
                    stringBuilder.AppendLine("  </tbody>");
                    stringBuilder.AppendLine("</table>");

                    userEmailContent.MailContent = userEmailContent.MailContent.Replace($"{{{table.tableId}}}", stringBuilder.ToString());
                }
            }
        }

        #endregion

        /// <summary>
        /// 取得代理人業務
        /// </summary>
        public static List<MailRecipientResultModel> GetAgentReceiver(AgentEmail model, string receiver_type)
        {
            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);

            // 用來儲存收件人的清單
            List<MailRecipientResultModel> recipients = new List<MailRecipientResultModel>();

            TransferLegalHistory transferLegalHistory = null;
            TransferPicMain transferPicMain = null;
            FlowStepSigner flowStep = null;
            List<flow_step_signer> signerList = null;

            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {

                    //角色
                    case "02":
                        PUserRoleDataRepository pUserRoleDataRepository = new PUserRoleDataRepository();
                        List<PUserRole> pUserRoles = pUserRoleDataRepository.Query(new PUserRoleQueryCondition()
                        {
                            SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = receiver.Value, Field = "r_id" }] }
                        });
                        if (pUserRoles != null && pUserRoles.Count > 0)
                        {
                            foreach (PUserRole role in pUserRoles)
                            {
                                if (!string.IsNullOrEmpty(role.UId))
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = role.UId, recipient_deptid = GetEmpl(role.UId).deptid });
                            }
                        }
                        break;
                    //總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalEMail, recipient_deptid = "" });
                        break;
                    //Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = adminEMail, recipient_deptid = adminEMail });
                        break;
                    case "18":
                        PsSubEeLglVwA agent = PsSubEeLglVwADataService.FindByKey(model.agent_empid);
                        if (agent != null)
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = agent.Emplid, recipient_deptid = agent.Deptid });
                        break;
                    //原經辦部門主管
                    case "19":

                        PsSubEeLglVwA auth = PsSubEeLglVwADataService.FindByKey(model.auth_empid);
                        if (auth != null)
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = auth.Emplid, recipient_deptid = auth.Deptid });
                        break;
                    //操作人
                    case "21":

                        PsSubEeLglVwA admin = PsSubEeLglVwADataService.FindByKey(model.admin_emplid);
                        if (admin != null)
                            recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = admin.Emplid, recipient_deptid = admin.Deptid });
                        break;
                    default:
                        break;
                }
            }
            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }/// <summary>
         /// 取得代理人業務
         /// </summary>
        public static List<MailRecipientResultModel> GetJobReceiver(string receiver_type)
        {
            // 假設 receiver_type 是一個 JSON 字串，先將其反序列化為物件
            var receiverList = JsonConvert.DeserializeObject<List<Receiver>>(receiver_type);

            // 用來儲存收件人的清單
            List<MailRecipientResultModel> recipients = new List<MailRecipientResultModel>();

            foreach (var receiver in receiverList)
            {
                switch (receiver.Key)
                {

                    //角色
                    case "02":
                        PUserRoleDataRepository pUserRoleDataRepository = new PUserRoleDataRepository();
                        List<PUserRole> pUserRoles = pUserRoleDataRepository.Query(new PUserRoleQueryCondition()
                        {
                            SearchItemGroup = new SearchItemGroup() { Items = [new SearchItem() { Compare = CompareOperator.ARRAYIN, Values = receiver.Value, Field = "r_id" }] }
                        });
                        if (pUserRoles != null && pUserRoles.Count > 0)
                        {
                            foreach (PUserRole role in pUserRoles)
                            {
                                if (!string.IsNullOrEmpty(role.UId))
                                    recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = role.UId, recipient_deptid = GetEmpl(role.UId).deptid });
                            }
                        }
                        break;
                    //總信箱
                    case "08":
                        string legalEMail = AppSettingHelper.Configuration["legalEMail"] ?? "";
                        if (!string.IsNullOrEmpty(legalEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = legalEMail, recipient_deptid = "" });
                        break;
                    //Admin信箱
                    case "26":
                        string adminEMail = AppSettingHelper.Configuration["adminEMail"] ?? "";
                        if (!string.IsNullOrEmpty(adminEMail)) recipients.Add(new MailRecipientResultModel() { recipient_type = receiver.Key, recipient_emplid = adminEMail, recipient_deptid = adminEMail });
                        break;
                    default:
                        break;
                }
            }
            // 排除重複的收件人
            return recipients.Distinct().ToList();
        }

        public static string GetMailAddress(List<MailRecipientResultModel> recipients, int is_re_agent)
        {
            if (recipients == null || recipients.Count == 0) return string.Empty;

            //總信箱/自動化設定人員/Admin信箱無工號
            List<string> emailAddressType = new List<string> { "08", "25", "26" };

            //收件人的電子郵件地址
            List<string> emailAddresses = new List<string>();

            // 查詢每個收件人的電子郵件地址
            foreach (var emplid in recipients)
            {
                if (string.IsNullOrEmpty(emplid.recipient_emplid)) continue;

                //總信箱/自動化設定人員/Admin信箱
                if (emailAddressType.Contains(emplid.recipient_type))
                {
                    emailAddresses.Add(emplid.recipient_emplid);
                }
                else
                {
                    var employee = GetEmpl(emplid.recipient_emplid);
                    if (employee != null)
                    {
                        if (!string.IsNullOrEmpty(employee.email_address_a) && employee.email_address_a != "NULL")
                        {
                            emailAddresses.Add(employee.email_address_a);
                        }
                        //type 13為經辦人代理人，不應再寄送其代理人
                        if (emplid.recipient_type != "13" && is_re_agent == 1)
                        {
                            string AgentEmp = _applyPermissionRepository.GetAgenByEmp(emplid.recipient_emplid, emplid.recipient_deptid);
                            if (!string.IsNullOrEmpty(AgentEmp))
                            {
                                employee = GetEmpl(AgentEmp);
                                if (employee != null && !string.IsNullOrEmpty(employee.email_address_a) && employee.email_address_a != "NULL")
                                {
                                    emailAddresses.Add(employee.email_address_a);
                                }
                            }
                        }
                    }
                }
            }

            // 將收件人的電子郵件地址轉換為逗號分隔的字串
            return string.Join(";", emailAddresses.Distinct().ToList());
        }

        public string GetMailAddressV2(List<MailRecipientResultModel> recipients, bool is_re_agent)
        {
            if (recipients == null || recipients.Count == 0) return string.Empty;

            //總信箱/自動化設定人員/Admin信箱無工號
            List<string> emailAddressType = new List<string> { "08", "25", "26" };

            //收件人的電子郵件地址
            List<string> emailAddresses = new List<string>();


            var agentList = new List<AgentByEmp>();

            if (recipients.Any(x => x.recipient_type != "13") && is_re_agent)
            {
                agentList = _applyPermissionRepository.GetAgenByEmpToTransaction(recipients
                    .Where(x => x.recipient_type != "13").Select(r =>
                        new Interface.Api.Common.Model.SqlSugarModels.ps_sub_ee_lgl_vw_a
                        {
                            emplid = r.recipient_emplid,
                            deptid = r.recipient_deptid
                        }).ToList());
            }

            var emplids = new List<string>();
            emplids.AddRange(recipients.Select(e => e.recipient_emplid));
            emplids.AddRange(agentList.Select(a => a.agent_empid));

            var eeList = GetEmpl(emplids);

            // 查詢每個收件人的電子郵件地址
            foreach (var emplid in recipients)
            {
                if (string.IsNullOrEmpty(emplid.recipient_emplid)) continue;

                //總信箱/自動化設定人員/Admin信箱
                if (emailAddressType.Contains(emplid.recipient_type))
                {
                    emailAddresses.Add(emplid.recipient_emplid);
                }
                else
                {
                    var employee = eeList.FirstOrDefault(e => e.emplid == emplid.recipient_emplid);
                    if (employee != null)
                    {
                        if (!string.IsNullOrEmpty(employee.email_address_a) && employee.email_address_a != "NULL")
                        {
                            emailAddresses.Add(employee.email_address_a);
                        }

                        //type 13為經辦人代理人，不應再寄送其代理人
                        if (emplid.recipient_type != "13" && is_re_agent)
                        {
                            var agentEmp = agentList.FirstOrDefault(x =>
                                x.auth_deptid == emplid.recipient_deptid && x.agent_deptid == emplid.recipient_deptid);
                            if (agentEmp != null)
                            {
                                employee = eeList.FirstOrDefault(e => e.emplid == agentEmp.agent_empid);
                                if (employee != null && !string.IsNullOrEmpty(employee.email_address_a) &&
                                    employee.email_address_a != "NULL")
                                {
                                    emailAddresses.Add(employee.email_address_a);
                                }
                            }
                        }
                    }
                }
            }

            // 將收件人的電子郵件地址轉換為逗號分隔的字串
            return string.Join(";", emailAddresses.Distinct().ToList());
        }


        // 定義 Receiver 類別來匹配 JSON 結構
        public class Receiver
        {
            public string Key { get; set; }
            public List<string> Value { get; set; }
        }

        private static ps_sub_ee_lgl_vw_a GetEmpl(string emplid)
        {
            if (string.IsNullOrEmpty(emplid)) return null;

            return _psRepository.GetEE(emplid);
        }

        private List<ps_sub_ee_lgl_vw_a> GetEmpl(List<string> emplids)
        {
            var eeList = SqlSugarHelper.Db.Queryable<ps_sub_ee_lgl_vw_a>().Where(x => emplids.Contains(x.emplid))
                .ToList();
            return eeList;
        }


        private static string ConvertJsonToCommaSeparated(string jsonInput)
        {
            // 反序列化 JSON 字串為 List<string>
            List<string> items = JsonConvert.DeserializeObject<List<string>>(jsonInput);
            if (items == null || items.Count == 0) return string.Empty;

            // 排除空的項目
            items = items.Where(item => !string.IsNullOrEmpty(item)).ToList();

            // 將 List<string> 轉換為用逗號分隔的字串
            return string.Join(", ", items);
        }
    }
}
