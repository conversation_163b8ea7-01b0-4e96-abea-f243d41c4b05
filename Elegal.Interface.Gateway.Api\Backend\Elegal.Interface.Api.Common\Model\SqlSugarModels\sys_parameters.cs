﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///參數表
    ///</summary>
    [SugarTable("sys_parameters")]
    public partial class sys_parameters
    {
           public sys_parameters(){


           }
           /// <summary>
           /// Desc:序號；主鍵自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:參數code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string para_code {get;set;} = null!;

           /// <summary>
           /// Desc:參數中文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string para_name {get;set;} = null!;

           /// <summary>
           /// Desc:參數英文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string para_ename {get;set;} = null!;

           /// <summary>
           /// Desc:功能code
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string func_code {get;set;} = null!;

           /// <summary>
           /// Desc:功能中/英文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string fun_name {get;set;} = null!;

           /// <summary>
           /// Desc:語係：ZH-TW / EN-US
           /// Default:ZH-TW
           /// Nullable:False
           /// </summary>           
           public string lang_type {get;set;} = null!;

           /// <summary>
           /// Desc:是否使用；1：使用；0：禁用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int is_used {get;set;}

           /// <summary>
           /// Desc:是否可以讓用戶進行維護，1：可以；0：禁止
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int is_shared {get;set;}

           /// <summary>
           /// Desc:根據 public.sys_function.funcode 自動排序
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sort_order {get;set;}

           /// <summary>
           /// Desc:備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? func_remarks {get;set;}

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:參數值類型：功能 func_code 對應的是文本還是數字	1：文本	2：數字
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int func_type {get;set;}

           /// <summary>
           /// Desc:參數類別：0：單值；1：多值
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int para_type {get;set;}

    }
}
