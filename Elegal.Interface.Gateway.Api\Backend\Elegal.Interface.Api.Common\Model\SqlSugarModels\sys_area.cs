﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///區域對照表
    ///</summary>
    [SugarTable("sys_area")]
    public partial class sys_area
    {
           public sys_area(){


           }
           /// <summary>
           /// Desc:主鍵，id，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int area_id {get;set;}

           /// <summary>
           /// Desc:區域名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string area_name {get;set;} = null!;

           /// <summary>
           /// Desc:創建人，操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人，操作者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
