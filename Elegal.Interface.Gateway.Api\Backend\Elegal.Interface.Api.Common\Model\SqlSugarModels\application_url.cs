﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///申請單超連接路徑
    ///</summary>
    [SugarTable("application_url")]
    public partial class application_url
    {
           public application_url(){


           }
           /// <summary>
           /// Desc:申請單號，對應各個申請單
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:超鏈接路徑
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string hyperlink_path {get;set;} = null!;

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:其他申請單結案時的授權檢視頁面鏈接
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? view_auth_link {get;set;}

    }
}
