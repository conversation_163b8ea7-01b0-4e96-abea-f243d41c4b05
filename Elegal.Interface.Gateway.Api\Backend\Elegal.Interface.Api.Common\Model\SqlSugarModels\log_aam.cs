﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///1.0使用者異動記錄數據
    ///</summary>
    [SugarTable("log_aam")]
    public partial class log_aam
    {
           public log_aam(){


           }
           /// <summary>
           /// Desc:異動ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public int change_id {get;set;}

           /// <summary>
           /// Desc:異動日期
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime change_date {get;set;}

           /// <summary>
           /// Desc:異動者公司ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company_id {get;set;} = null!;

           /// <summary>
           /// Desc:公司名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string company_name {get;set;} = null!;

           /// <summary>
           /// Desc:部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:部門中文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? dept_descr {get;set;}

           /// <summary>
           /// Desc:異動者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string empid {get;set;} = null!;

           /// <summary>
           /// Desc:異動者姓名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string name {get;set;} = null!;

           /// <summary>
           /// Desc:原使用者公司ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_comp_id {get;set;}

           /// <summary>
           /// Desc:原使用者公司名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_comp_descr {get;set;}

           /// <summary>
           /// Desc:原使用者部門代號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_dept_id {get;set;}

           /// <summary>
           /// Desc:原使用者部門中文名稱
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_dept_descr {get;set;}

           /// <summary>
           /// Desc:原使用者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_emp_id {get;set;}

           /// <summary>
           /// Desc:原使用者姓名
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ori_name {get;set;}

           /// <summary>
           /// Desc:作業項目
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? work_item {get;set;}

           /// <summary>
           /// Desc:異動類型	A-關企	B-合約	D-文件	AR-關企建檔	R-資料建檔
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string change_type {get;set;} = null!;

           /// <summary>
           /// Desc:異動欄位
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_item {get;set;}

           /// <summary>
           /// Desc:異動內容
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string change_content {get;set;} = null!;

           /// <summary>
           /// Desc:檔案位置
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? change_link {get;set;}

           /// <summary>
           /// Desc:合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:是否修改附件，1：是；0：否
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? has_append {get;set;}

    }
}
