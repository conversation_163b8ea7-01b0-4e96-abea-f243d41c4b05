﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///簽核流程例外主體字典(分組主體)
    ///</summary>
    [SugarTable("special_entity_group")]
    public partial class special_entity_group
    {
           public special_entity_group(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsIdentity=true)]
           public long rowid {get;set;}

           /// <summary>
           /// Desc:主體ID
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:主體簡稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity {get;set;} = null!;

           /// <summary>
           /// Desc:分組主體簡稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string group_type {get;set;} = null!;

    }
}
