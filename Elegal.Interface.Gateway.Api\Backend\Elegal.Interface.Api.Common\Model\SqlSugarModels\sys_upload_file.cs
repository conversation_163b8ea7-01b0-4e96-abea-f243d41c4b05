﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///附件上傳對照表
    ///</summary>
    [SugarTable("sys_upload_file")]
    public partial class sys_upload_file
    {
           public sys_upload_file(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int fileid {get;set;}

           /// <summary>
           /// Desc:上傳類型	1：公告附件	2：合约申請附件	3：客戶暱稱	4：資料建檔	5：其他申請	6：關企建檔	101：表示水印文件與正常業務無關
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int upload_type {get;set;}

           /// <summary>
           /// Desc:上傳主鍵：公告附件為newid，申請作業附件為申請單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string upload_key {get;set;} = null!;

           /// <summary>
           /// Desc:文件名(用戶上傳時的文件名，非系統修改後的文件名)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string file_name {get;set;} = null!;

           /// <summary>
           /// Desc:文件大小
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal file_size {get;set;}

           /// <summary>
           /// Desc:文件類型
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string file_type {get;set;} = null!;

           /// <summary>
           /// Desc:文件路徑(上傳後的服務器路徑)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string file_path {get;set;} = null!;

           /// <summary>
           /// Desc:文件說明
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? file_explain {get;set;}

           /// <summary>
           /// Desc:是否為臨時文件：0：永久文件；1：臨時文件	1、在申請作業時：用戶單純上傳文件的狀態為1，當用戶點選核准操作後，才會將狀態修改為0	2、在公共附件上傳時：用戶單純上傳文件的狀態為1，當用戶點選核准操作後，才會將狀態修改為0
           /// Default:
           /// Nullable:True
           /// </summary>           
           public bool? is_temporary {get;set;}

           /// <summary>
           /// Desc:壓縮檔的id，如批次上傳的文件中包含壓縮檔，則需要將文件解壓後見壓縮後的附件逐筆添加，這個欄位就需要填寫
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? parent_id {get;set;}

           /// <summary>
           /// Desc:創建人，操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:是否可以转档(0：否；1：是)
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool has_watermake {get;set;}

           /// <summary>
           /// Desc:档案用途	upload_type：2	1：列印合約	2：參考資料	3：歸檔	4：法務行政交接備註档案附件	101：表示臨時文件與任何模塊無關
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? archive_purposes {get;set;}

           /// <summary>
           /// Desc:是否是转档文件(0：否；1：是)
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_watermake {get;set;}

           /// <summary>
           /// Desc:檔案狀態(upload_type為2、is_watermake為0時)	0：無轉檔檔；	1：轉檔中；	2：已轉檔；	3：轉檔失敗；	105：表示數據庫有，但是文件在minio不存在，正在上傳中；	501：表示上傳失敗（一般表示水印文件生成失敗）；	106：表示上傳失敗，重新上傳中；	502：表示失敗了第二次；	200：表示正常文件狀態
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int file_status {get;set;}

           /// <summary>
           /// Desc:原档文件id(upload_type为2时，此为转档文件，存储原档id)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? original_file_id {get;set;}

           /// <summary>
           /// Desc:申請單附檔失效日期	進行中：失效日期為空	已核准後的(A/F)：當前日期+7天(讀參數管理)	合約申請單簽核完成(E): 核准日+7天(讀參數管理)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? expiry_date {get;set;}

           /// <summary>
           /// Desc:是否需要根據upload_key來重新定義地址欄，0需要，1不需要
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool need_update_path {get;set;}

           /// <summary>
           /// Desc:轉檔次數，失敗一次就加一，失敗五次就不轉檔
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? convert_count {get;set;}

    }
}
