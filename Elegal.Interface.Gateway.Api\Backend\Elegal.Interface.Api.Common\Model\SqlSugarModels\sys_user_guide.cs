﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///系統引導頁對照表
    ///</summary>
    [SugarTable("sys_user_guide")]
    public partial class sys_user_guide
    {
           public sys_user_guide(){


           }
           /// <summary>
           /// Desc:標識
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string id {get;set;} = null!;

           /// <summary>
           /// Desc:當前引導頁服務的菜單欄id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string menu_code {get;set;} = null!;

           /// <summary>
           /// Desc:右邊距
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? mright {get;set;}

           /// <summary>
           /// Desc:上邊距
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? mtop {get;set;}

           /// <summary>
           /// Desc:寬度
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int width {get;set;}

           /// <summary>
           /// Desc:高度
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int height {get;set;}

           /// <summary>
           /// Desc:引導標題
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? tip_title {get;set;}

           /// <summary>
           /// Desc:引導語
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? tip_text {get;set;}

           /// <summary>
           /// Desc:guid使用圖片
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? tip_img {get;set;}

           /// <summary>
           /// Desc:提示語框體寬度(高度自適應)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int tip_width {get;set;}

           /// <summary>
           /// Desc:是否顯示下一步；1：啟用，0：停用
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? can_next {get;set;}

           /// <summary>
           /// Desc:是否顯示上一步；1：啟用，0：停用
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? can_previous {get;set;}

           /// <summary>
           /// Desc:是否顯示略過；1：啟用，0：停用
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? can_skip {get;set;}

           /// <summary>
           /// Desc:是否顯示我知道了；1：啟用，0：停用
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? can_finish {get;set;}

           /// <summary>
           /// Desc:高亮區塊是否可點擊；1：啟用，0：停用
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? can_highlight_click {get;set;}

           /// <summary>
           /// Desc:下邊距
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? mbottom {get;set;}

           /// <summary>
           /// Desc:左邊距
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? mleft {get;set;}

           /// <summary>
           /// Desc:排序
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int sort_order {get;set;}

           /// <summary>
           /// Desc:該步驟是否啟用；1：啟用，0：停用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool used {get;set;}

           /// <summary>
           /// Desc:該版本是否啟用引導；1：啟用，0：停用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public bool enable {get;set;}

           /// <summary>
           /// Desc:引導標題(英文)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? tip_etitle {get;set;}

           /// <summary>
           /// Desc:引導語(英文)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? tip_etext {get;set;}

    }
}
