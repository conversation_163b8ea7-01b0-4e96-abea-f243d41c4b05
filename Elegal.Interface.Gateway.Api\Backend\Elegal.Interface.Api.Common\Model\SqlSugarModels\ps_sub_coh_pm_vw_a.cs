﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///CoHead表
    ///</summary>
    [SugarTable("ps_sub_coh_pm_vw_a")]
    public partial class ps_sub_coh_pm_vw_a
    {
           public ps_sub_coh_pm_vw_a(){


           }
           /// <summary>
           /// Desc:部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string deptid {get;set;} = null!;

           /// <summary>
           /// Desc:主管名稱
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string manager_id {get;set;} = null!;

           /// <summary>
           /// Desc:副主管順序，一個部門可以有多個副主管
           /// Default:
           /// Nullable:False
           /// </summary>           
           public decimal seq_no {get;set;}

    }
}
