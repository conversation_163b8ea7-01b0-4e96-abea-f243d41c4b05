﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///借出申請(主表)
    ///</summary>
    [SugarTable("paper_lending_application")]
    public partial class paper_lending_application
    {
           public paper_lending_application(){


           }
           /// <summary>
           /// Desc:借出id，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int lend_id {get;set;}

           /// <summary>
           /// Desc:借出單號(規則：L+西元年+5碼流水號(次年重新計算))
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_number {get;set;}

           /// <summary>
           /// Desc:填單人工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string lend_fill_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:填單人部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string lend_fill_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:經辦人工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string lend_handler_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:經辦人部門代號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string lend_handler_deptid {get;set;} = null!;

           /// <summary>
           /// Desc:申請單狀態，參數表 sys_parameters.para_code = lib_lendStatus	1、暫存(01)：新增申請單保存未提交，以及正本紙本調閱申請結案後系統自動產生的申請單[暫存的單:有刪除按鈕。]（由調閱單產生的暫存單有申請單號）	2、待取件(02)：申請單提交後，申請單狀態改為：待取件。紙本的入庫狀態改為：預約中。經辦人可以在首頁提醒及資料查詢中查看當前借閱清單。	3、出借中(03)：經辦人嗶咔自取或法務行政填託運單號後，申請單狀態改為：出借中。紙本的入庫狀態改為：出借中。	4、已結案(04)：申請單中的紙本全部歸還後，狀態自動變更為：已結案	5、已作廢(05)：暫存的調閱單被刪除、或超過調閱期限未領取的調閱單被刪除、或已提交的借出申請單被刪除
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string lend_status {get;set;} = null!;

           /// <summary>
           /// Desc:作廢原因，當申請單狀態為作廢時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? void_reason {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:填單人 BO\BU 業務單位
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_fill_bu {get;set;}

           /// <summary>
           /// Desc:填單人事業群
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_fill_bg {get;set;}

           /// <summary>
           /// Desc:經辦人 BO\BU 業務單位
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_handler_bu {get;set;}

           /// <summary>
           /// Desc:經辦人 事業群
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? lend_handler_bg {get;set;}

           /// <summary>
           /// Desc:非調閱申請單結案時系統生成的暫存單
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? temp_lend_number {get;set;}

           /// <summary>
           /// Desc:申請日期，初始加載時為當前時間，當有暫存單提交時，需要修改
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime application_time {get;set;}

    }
}
