﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///用印核決權限表
    ///</summary>
    [SugarTable("legal_assign_permission")]
    public partial class legal_assign_permission
    {
           public legal_assign_permission(){


           }
           /// <summary>
           /// Desc:主體分類 1. 緯創幕僚單位 2.緯創總經理室底下 3. 緯創資通總經理底下部門的子公司  4. 子公司(1.2.3除外)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_type {get;set;} = null!;

           /// <summary>
           /// Desc:合約性質
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string contract_type {get;set;} = null!;

           /// <summary>
           /// Desc:總經理核決權限 1:一定簽核 2.根據核決條件判斷是否需要簽核 0.無規則，不啟用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string legal_assigngm {get;set;} = null!;

           /// <summary>
           /// Desc:董事長核決權限 1:一定簽核 2.根據核決條件判斷是否需要簽核 0.無規則，不啟用
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string legal_assignceo {get;set;} = null!;

           /// <summary>
           /// Desc:總經理核決金額
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? rules_gmlowprice {get;set;}

           /// <summary>
           /// Desc:董事長核決金額
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? rules_ceolowprice {get;set;}

           /// <summary>
           /// Desc:文件分類，默認為空
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? file_type {get;set;}

           /// <summary>
           /// Desc:CFO金額簽核，默認為0
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public int? cfo_price {get;set;}

           /// <summary>
           /// Desc:CFO簽核，默認為1
           /// Default:1
           /// Nullable:True
           /// </summary>           
           public int? cfo_approve {get;set;}

    }
}
