﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///所屬地對應表
    ///</summary>
    [SugarTable("ps_sub_loc_vw_a")]
    public partial class ps_sub_loc_vw_a
    {
           public ps_sub_loc_vw_a(){


           }
           /// <summary>
           /// Desc:所屬地代碼
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string location {get;set;} = null!;

           /// <summary>
           /// Desc:說明
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string descr {get;set;} = null!;

           /// <summary>
           /// Desc:工作所在地
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string site_id_a {get;set;} = null!;

           /// <summary>
           /// Desc:區碼
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? prefix_dial_code_a {get;set;}

    }
}
