﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///合約申請 -> 賦予合約編號
    ///</summary>
    [SugarTable("form_contract_number")]
    public partial class form_contract_number
    {
           public form_contract_number(){


           }
           /// <summary>
           /// Desc:申請單號，與 form_application.apply_number 關聯
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:主體id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string entity_id {get;set;} = null!;

           /// <summary>
           /// Desc:年
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? year {get;set;}

           /// <summary>
           /// Desc:編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? num {get;set;}

           /// <summary>
           /// Desc:子編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? childnum {get;set;}

           /// <summary>
           /// Desc:拼接好的合約編號	1、主體簡稱：必填。合約編號主體簡稱。不可修改	2、年度：必填。西元年（4碼）。默認生效日年份，生效日為空時默認當前年份。可修改。	3、編號：必填。4碼流水號	4、子編號：非必填。默認為空，可輸入數字和-，至多輸入10字元	5、若有REF主約，自動預設合約編號為：REF主約編號-子編號（即：該REF主約下所有子編號的最大值+1）。	可修改	EX：總部承辦法務”區塊填寫的“REF主約(相關合約)編號/申請單號為”：WHQ20250001，則自動預設合約編號為：WHQ20250001-1，若歷史資料中已有WHQ20250001-1，則預設WHQ20250001-2	格式：主體簡稱 + 年 + 編號 + N'-' + 子編號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string contract_number {get;set;} = null!;

           /// <summary>
           /// Desc:ref主約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? ref_number {get;set;}

           /// <summary>
           /// Desc:主約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? main_contract_number {get;set;}

           /// <summary>
           /// Desc:1.0轉移過來的主體ID
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? old_entity_id {get;set;}

    }
}
