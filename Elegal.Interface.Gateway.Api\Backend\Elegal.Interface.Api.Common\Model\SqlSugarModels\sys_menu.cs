﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///菜單欄(原始資料表p_function)
    ///</summary>
    [SugarTable("sys_menu")]
    public partial class sys_menu
    {
           public sys_menu(){


           }
           /// <summary>
           /// Desc:序號；自增長，主鍵
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:父級菜單ID，如 menulevel == 1 需要默認為0
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public int menu_parent_code {get;set;}

           /// <summary>
           /// Desc:菜單ID(原始數據為f_id)
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int menu_code {get;set;}

           /// <summary>
           /// Desc:菜單中/英文名
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string menu_name {get;set;} = null!;

           /// <summary>
           /// Desc:菜單部件名，ex：HomeComponent
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string menu_component {get;set;} = null!;

           /// <summary>
           /// Desc:菜單欄圖標
           /// Default:appstore
           /// Nullable:False
           /// </summary>           
           public string menu_icon {get;set;} = null!;

           /// <summary>
           /// Desc:語係：ZH-TW / EN-US
           /// Default:ZH-TW
           /// Nullable:False
           /// </summary>           
           public string lang_type {get;set;} = null!;

           /// <summary>
           /// Desc:菜單欄等級，1：一級菜單；2：二級菜單；3：三級菜單
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int menu_level {get;set;}

           /// <summary>
           /// Desc:是否使用；1：使用；0：禁用
           /// Default:1
           /// Nullable:False
           /// </summary>           
           public int is_used {get;set;}

           /// <summary>
           /// Desc:創建人；登陸者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間，默認當前時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人；登陸者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:排序，為頁面顯示準備
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? sort_order {get;set;}

    }
}
