﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///需求資訊(調閱單號資訊)[擴展表]
    ///</summary>
    [SugarTable("paper_lending_demand")]
    public partial class paper_lending_demand
    {
           public paper_lending_demand(){


           }
           /// <summary>
           /// Desc:調閱明細id，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int demand_id {get;set;}

           /// <summary>
           /// Desc:主表paper_lending_application.lend_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int paper_lend_id {get;set;}

           /// <summary>
           /// Desc:調閱單號，其他申請的申請單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? retrieve_number {get;set;}

           /// <summary>
           /// Desc:借出天數(不可超過借出明細中借閱日上限的最小值)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? borrow_days {get;set;}

           /// <summary>
           /// Desc:申請原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? demand_reason {get;set;}

           /// <summary>
           /// Desc:調閱申請單的其他說明和備註，累加，第一次以調閱申請單的備註為主，後面則以當前欄位為主
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? retrieve_reason {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:修改人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

    }
}
