﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///用戶角色對照表(原始資料表p_usersrole)
    ///</summary>
    [SugarTable("p_user_role")]
    public partial class p_user_role
    {
           public p_user_role(){


           }
           /// <summary>
           /// Desc:主鍵，序號，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string u_id {get;set;} = null!;

           /// <summary>
           /// Desc:角色id，對應p_role.r_id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int r_id {get;set;}

           /// <summary>
           /// Desc:單號(後期會驗證再看是否需要)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_number {get;set;}

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:創建人；操作者工號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:修改時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? modify_time {get;set;}

           /// <summary>
           /// Desc:修改人；操作者工號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? modify_user {get;set;}

    }
}
