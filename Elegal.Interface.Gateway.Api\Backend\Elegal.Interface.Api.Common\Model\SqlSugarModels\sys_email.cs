﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///發送郵件
    ///</summary>
    [SugarTable("sys_email")]
    public partial class sys_email
    {
           public sys_email(){


           }
           /// <summary>
           /// Desc:序號，主鍵
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true)]
           public Guid e_id {get;set;}

           /// <summary>
           /// Desc:郵件標題
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string e_subject {get;set;} = null!;

           /// <summary>
           /// Desc:郵件內容
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string e_content {get;set;} = null!;

           /// <summary>
           /// Desc:收件人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string e_receiver {get;set;} = null!;

           /// <summary>
           /// Desc:發件人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? e_sender {get;set;}

           /// <summary>
           /// Desc:抄送人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? e_cc {get;set;}

           /// <summary>
           /// Desc:時間
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime e_sendtime {get;set;}

           /// <summary>
           /// Desc:已發送次數
           /// Default:
           /// Nullable:False
           /// </summary>           
           public long e_sendnum {get;set;}

           /// <summary>
           /// Desc:是否發送
           /// Default:
           /// Nullable:False
           /// </summary>           
           public bool e_issend {get;set;}

           /// <summary>
           /// Desc:mail種類
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string e_type {get;set;} = null!;

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_number {get;set;}

           /// <summary>
           /// Desc:申請單號的時間
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_time {get;set;}

           /// <summary>
           /// Desc:申請單號的經辦人
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? emplid {get;set;}

           /// <summary>
           /// Desc:申請單號的合約編號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? contract_number {get;set;}

           /// <summary>
           /// Desc:申請單號的URL
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_url {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_case {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_empl {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_ccj_empl {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_ccj_org {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_og {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_supervisor {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_upog {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_is_uporg {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_ccj_repeat {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_co_head {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_special_og {get;set;}

           /// <summary>
           /// Desc:
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? abnormal_special_empl {get;set;}

    }
}
