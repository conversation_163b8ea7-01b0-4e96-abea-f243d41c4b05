﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///申請單簽核待簽核人員
    ///</summary>
    [SugarTable("flow_step_signer")]
    public partial class flow_step_signer
    {
           public flow_step_signer(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public int rowid {get;set;}

           /// <summary>
           /// Desc:流程代號	1-一般合約	2-資金合約	3-人力資源合約
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int flow_id {get;set;}

           /// <summary>
           /// Desc:簽核流程id
           /// Default:
           /// Nullable:False
           /// </summary>           
           public int step_id {get;set;}

           /// <summary>
           /// Desc:申請單號
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:簽核時間
           /// Default:
           /// Nullable:False
           /// </summary>           
           public DateTime signer_time {get;set;}

           /// <summary>
           /// Desc:簽核人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string signer_emplid {get;set;} = null!;

           /// <summary>
           /// Desc:簽核人部門
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? signer_deptid {get;set;}

           /// <summary>
           /// Desc:是否被return；0：不是；1：是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_return {get;set;}

           /// <summary>
           /// Desc:是否加簽；0：未加簽；1：加簽
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_invitee {get;set;}

           /// <summary>
           /// Desc:是否會簽；0：未會簽；1：會簽
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_acknowledge {get;set;}

           /// <summary>
           /// Desc:是否有錯；0：無錯；1：有錯
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_error {get;set;}

           /// <summary>
           /// Desc:是否被reject；0：不是；1：是
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_reject {get;set;}

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:創建人
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:return動作時，記錄需要返回的流程
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? return_step_id {get;set;}

           /// <summary>
           /// Desc:是否有被MCP簽核；0：沒有；1：有
           /// Default:0
           /// Nullable:False
           /// </summary>           
           public bool is_sign_mcp {get;set;}

           /// <summary>
           /// Desc:會簽詳細步驟
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? acknowledge_step {get;set;}

    }
}
