﻿using System;
using System.Linq;
using System.Text;
using SqlSugar;

namespace Elegal.Interface.Api.Common.Model.SqlSugarModels
{
    ///<summary>
    ///其他申請 -> A)：案件調閱
    ///</summary>
    [SugarTable("other_application_a")]
    public partial class other_application_a
    {
           public other_application_a(){


           }
           /// <summary>
           /// Desc:序號，主鍵，自增長
           /// Default:
           /// Nullable:False
           /// </summary>           
           [SugarColumn(IsPrimaryKey=true,IsIdentity=true)]
           public long rowid {get;set;}

           /// <summary>
           /// Desc:申請單號，與 other_application.apply_number 對應
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string apply_number {get;set;} = null!;

           /// <summary>
           /// Desc:調閱類型；sys_parameters.para_code = N'otherDocType'
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string doc_type {get;set;} = null!;

           /// <summary>
           /// Desc:正本紙本原因，當 doc_type = N'02' 時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? original_paper_reason {get;set;}

           /// <summary>
           /// Desc:是否需要下載權限，0：否，1：是，當 doc_type = N'01' 時，必填
           /// Default:0
           /// Nullable:True
           /// </summary>           
           public bool? has_download {get;set;}

           /// <summary>
           /// Desc:下載權限原因，當 has_download = 1 時，必填
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? download_reason {get;set;}

           /// <summary>
           /// Desc:調閱主體，單筆
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? retrieve_entity_id {get;set;}

           /// <summary>
           /// Desc:調閱單號
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? retrieve_apply_number {get;set;}

           /// <summary>
           /// Desc:申請原因
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_reason {get;set;}

           /// <summary>
           /// Desc:申請開放期間(起日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_date_start {get;set;}

           /// <summary>
           /// Desc:申請開放期間(迄日)
           /// Default:
           /// Nullable:True
           /// </summary>           
           public DateTime? apply_date_end {get;set;}

           /// <summary>
           /// Desc:其他說明/備註
           /// Default:
           /// Nullable:True
           /// </summary>           
           public string? apply_remark {get;set;}

           /// <summary>
           /// Desc:創建人員
           /// Default:
           /// Nullable:False
           /// </summary>           
           public string create_user {get;set;} = null!;

           /// <summary>
           /// Desc:創建時間
           /// Default:DateTime.Now
           /// Nullable:False
           /// </summary>           
           public DateTime create_time {get;set;}

           /// <summary>
           /// Desc:申請開放日期差值
           /// Default:
           /// Nullable:True
           /// </summary>           
           public int? apply_open_time {get;set;}

    }
}
